#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from 数据库管理器 import 数据库管理器

def 检查日期数据():
    """检查数据库中的日期数据"""
    db = 数据库管理器()
    
    print("=== 7月份的交易日期 ===")
    sql = "SELECT DISTINCT trade_date FROM 日线行情 WHERE trade_date LIKE '202407%' ORDER BY trade_date DESC"
    dates = db.查询数据(sql)
    print(dates)
    
    print("\n=== 检查特定日期数据量 ===")
    # 检查7月21日数据
    sql_21 = "SELECT COUNT(*) as count FROM 日线行情 WHERE trade_date = '20240721'"
    result_21 = db.查询数据(sql_21)
    print(f"7月21日数据量: {result_21.iloc[0]['count']}")
    
    # 检查7月31日数据
    sql_31 = "SELECT COUNT(*) as count FROM 日线行情 WHERE trade_date = '20240731'"
    result_31 = db.查询数据(sql_31)
    print(f"7月31日数据量: {result_31.iloc[0]['count']}")
    
    print("\n=== 最新的10个交易日期 ===")
    sql_latest = "SELECT DISTINCT trade_date FROM 日线行情 ORDER BY trade_date DESC LIMIT 10"
    latest = db.查询数据(sql_latest)
    print(latest)
    
    print("\n=== 检查API返回的可用日期 ===")
    sql_available = "SELECT DISTINCT trade_date FROM 日线行情 ORDER BY trade_date DESC LIMIT 100"
    available = db.查询数据(sql_available)
    print(f"可用日期总数: {len(available)}")
    print("前10个日期:")
    print(available.head(10))

if __name__ == "__main__":
    检查日期数据()
