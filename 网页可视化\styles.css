/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', 'SimHei', <PERSON><PERSON>, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

/* 导航栏样式 */
.navbar {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-logo h1 {
    color: #667eea;
    font-size: 1.5rem;
    font-weight: 600;
}

.nav-menu {
    display: flex;
    gap: 1rem;
}

.nav-btn {
    padding: 0.5rem 1.5rem;
    border: none;
    background: transparent;
    color: #666;
    cursor: pointer;
    border-radius: 25px;
    transition: all 0.3s ease;
    font-weight: 500;
}

.nav-btn:hover {
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
}

.nav-btn.active {
    background: #667eea;
    color: white;
}

/* 主要内容区域 */
.main-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* 页面头部 */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.page-header h2 {
    color: white;
    font-size: 2rem;
    font-weight: 600;
}

.refresh-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.refresh-btn:hover {
    background: rgba(255, 255, 255, 0.3);
}

/* 统计卡片网格 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: rgba(255, 255, 255, 0.95);
    padding: 1.5rem;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-card.up {
    border-left: 4px solid #4CAF50;
}

.stat-card.down {
    border-left: 4px solid #f44336;
}

.stat-title {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.stat-value {
    color: #333;
    font-size: 1.8rem;
    font-weight: 600;
}

/* 图表网格 */
.charts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.chart-container {
    background: rgba(255, 255, 255, 0.95);
    padding: 1.5rem;
    border-radius: 15px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.chart-container.full-width {
    grid-column: 1 / -1;
}

.chart-container h3 {
    color: #333;
    margin-bottom: 1rem;
    font-size: 1.2rem;
}

.chart {
    width: 100%;
    height: 400px;
}

.chart.large {
    height: 500px;
}

.chart.medium {
    height: 300px;
}

/* 个股控制面板 */
.stock-controls {
    display: flex;
    gap: 1rem;
    align-items: center;
    flex-wrap: wrap;
}

.stock-controls select,
.stock-controls input,
.stock-controls button {
    padding: 0.5rem 1rem;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: 0.9rem;
}

.stock-controls button {
    background: #667eea;
    color: white;
    border: none;
    cursor: pointer;
    transition: background 0.3s ease;
}

.stock-controls button:hover {
    background: #5a6fd8;
}

/* 股票信息卡片 */
.stock-info-card {
    background: rgba(255, 255, 255, 0.95);
    padding: 1.5rem;
    border-radius: 15px;
    margin-bottom: 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.stock-basic h3 {
    color: #333;
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}

.stock-code {
    color: #666;
    font-size: 0.9rem;
}

.stock-stats {
    display: flex;
    gap: 2rem;
}

.stock-stat {
    text-align: center;
}

.stock-stat .label {
    display: block;
    color: #666;
    font-size: 0.8rem;
    margin-bottom: 0.25rem;
}

.stock-stat .value {
    display: block;
    color: #333;
    font-size: 1.2rem;
    font-weight: 600;
}

/* 表格样式 */
.table-container {
    background: rgba(255, 255, 255, 0.95);
    padding: 1.5rem;
    border-radius: 15px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
}

.table-container h3 {
    color: #333;
    margin-bottom: 1rem;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.9rem;
}

.data-table th,
.data-table td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.data-table th {
    background: #f8f9fa;
    color: #333;
    font-weight: 600;
}

.data-table tr:hover {
    background: #f8f9fa;
}

/* 加载动画 */
.loading {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.loading-text {
    color: white;
    margin-top: 1rem;
    font-size: 1.1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 错误提示 */
.error-message {
    position: fixed;
    top: 20px;
    right: 20px;
    background: #f44336;
    color: white;
    padding: 1rem;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
    z-index: 9999;
    max-width: 400px;
}

.error-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.error-close {
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    margin-left: 1rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .nav-container {
        flex-direction: column;
        gap: 1rem;
    }
    
    .nav-menu {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .main-content {
        padding: 1rem;
    }
    
    .page-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .charts-grid {
        grid-template-columns: 1fr;
    }
    
    .stock-controls {
        justify-content: center;
    }
    
    .stock-info-card {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .stock-stats {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .stock-stats {
        flex-direction: column;
        gap: 1rem;
    }
    
    .data-table {
        font-size: 0.8rem;
    }
    
    .data-table th,
    .data-table td {
        padding: 0.5rem;
    }
}

/* 数据详情页面样式 */
.selector-container {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-wrap: wrap;
    gap: 2rem;
    align-items: end;
}

.selector-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    min-width: 200px;
}

.selector-group label {
    font-weight: 600;
    color: #333;
    font-size: 0.9rem;
}

.stock-select, .date-select {
    padding: 0.75rem;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 1rem;
    background: white;
    transition: all 0.3s ease;
}

.stock-select:focus, .date-select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.action-btn {
    padding: 0.75rem 1.5rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    height: fit-content;
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

/* 日期导航样式 */
.date-navigation {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-date-btn {
    padding: 0.75rem 1.5rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.nav-date-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.nav-date-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.current-date {
    font-size: 1.2rem;
    font-weight: 600;
    color: #333;
    background: rgba(102, 126, 234, 0.1);
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
}

/* 数据详情容器样式 */
.detail-container {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.detail-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding: 1rem;
    background: rgba(102, 126, 234, 0.1);
    border-radius: 10px;
}

.stock-info {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.stock-info span:first-child {
    font-size: 1.2rem;
    font-weight: 600;
    color: #333;
}

.stock-info span:last-child {
    font-size: 1rem;
    color: #666;
}

.date-info {
    font-size: 1.1rem;
    font-weight: 600;
    color: #667eea;
}

/* 详情表格样式 */
.detail-table {
    width: 100%;
}

.detail-table th:first-child,
.detail-table td:first-child {
    width: 25%;
    font-weight: 600;
}

.detail-table th:nth-child(2),
.detail-table td:nth-child(2) {
    width: 25%;
    text-align: right;
    font-weight: 600;
}

.detail-table th:last-child,
.detail-table td:last-child {
    width: 50%;
    color: #666;
    font-size: 0.9rem;
}

/* 无数据提示样式 */
.no-data-message {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 3rem;
    text-align: center;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.no-data-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
}

.no-data-text {
    font-size: 1.5rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 1rem;
}

.no-data-hint {
    font-size: 1rem;
    color: #666;
}

/* 股票数据总览页面样式 */
.search-filter-container {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.search-group {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
    align-items: center;
}

.search-input {
    flex: 1;
    padding: 0.75rem 1rem;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.search-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.search-btn, .filter-btn, .clear-btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.search-btn, .filter-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.search-btn:hover, .filter-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.clear-btn {
    background: #f8f9fa;
    color: #666;
    border: 2px solid #e1e5e9;
}

.clear-btn:hover {
    background: #e9ecef;
    border-color: #adb5bd;
}

.filter-group {
    display: flex;
    gap: 1rem;
    align-items: center;
    flex-wrap: wrap;
}

.filter-select {
    padding: 0.75rem 1rem;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 1rem;
    background: white;
    cursor: pointer;
    transition: all 0.3s ease;
}

.filter-select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* 统计信息样式 */
.stats-container {
    display: flex;
    gap: 2rem;
    margin-bottom: 2rem;
    flex-wrap: wrap;
}

.stat-item {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    flex: 1;
    min-width: 200px;
}

.stat-label {
    display: block;
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 0.5rem;
}

.stat-value {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
    color: #333;
}

/* 股票数据表格样式 */
.stocks-table-container {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.table-wrapper {
    overflow-x: auto;
    border-radius: 12px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.stocks-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    font-size: 0.9rem;
}

.stocks-table th {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem 0.75rem;
    text-align: left;
    font-weight: 600;
    position: sticky;
    top: 0;
    z-index: 10;
    cursor: pointer;
    user-select: none;
    transition: background 0.3s ease;
}

.stocks-table th:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

.stocks-table th.sortable::after {
    content: ' ↕️';
    font-size: 0.8rem;
    opacity: 0.7;
}

.stocks-table th.sort-asc::after {
    content: ' ↑';
    color: #ffd700;
}

.stocks-table th.sort-desc::after {
    content: ' ↓';
    color: #ffd700;
}

.stocks-table td {
    padding: 0.75rem;
    border-bottom: 1px solid #e1e5e9;
    transition: background 0.3s ease;
}

.stocks-table tbody tr:hover {
    background: rgba(102, 126, 234, 0.05);
}

.stocks-table tbody tr:nth-child(even) {
    background: rgba(248, 249, 250, 0.5);
}

.stocks-table tbody tr:nth-child(even):hover {
    background: rgba(102, 126, 234, 0.08);
}

/* 数值样式 */
.positive-change {
    color: #e74c3c;
    font-weight: 600;
}

.negative-change {
    color: #27ae60;
    font-weight: 600;
}

.neutral-change {
    color: #666;
}

/* 分页样式 */
.pagination-container {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 1.5rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.pagination-info {
    font-size: 0.9rem;
    color: #666;
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.page-btn {
    padding: 0.5rem 1rem;
    border: 2px solid #e1e5e9;
    background: white;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.page-btn:hover:not(:disabled) {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

.page-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.page-numbers {
    display: flex;
    gap: 0.25rem;
}

.page-number {
    padding: 0.5rem 0.75rem;
    border: 1px solid #e1e5e9;
    background: white;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.page-number:hover {
    background: #f8f9fa;
}

.page-number.active {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

.page-size-selector {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    color: #666;
}

.page-size-selector select {
    padding: 0.5rem;
    border: 2px solid #e1e5e9;
    border-radius: 6px;
    background: white;
    cursor: pointer;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .search-filter-container {
        padding: 1rem;
    }

    .search-group {
        flex-direction: column;
        align-items: stretch;
    }

    .filter-group {
        flex-direction: column;
        align-items: stretch;
    }

    .stats-container {
        flex-direction: column;
        gap: 1rem;
    }

    .stocks-table-container {
        padding: 1rem;
    }

    .stocks-table {
        font-size: 0.8rem;
    }

    .stocks-table th,
    .stocks-table td {
        padding: 0.5rem 0.25rem;
    }

    .pagination-container {
        flex-direction: column;
        align-items: stretch;
        text-align: center;
    }

    .pagination-controls {
        justify-content: center;
    }
}

/* 简化的数据详情页面样式 */
.controls-container {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    align-items: center;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.date-controls {
    display: flex;
    align-items: center;
    gap: 8px;
}

.date-controls label {
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
    font-weight: 500;
}

.search-input, .filter-select, #trade-date-select {
    padding: 8px 12px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    font-size: 14px;
    transition: border-color 0.2s ease;
}

.search-input {
    min-width: 200px;
}

.search-input:focus, .filter-select:focus, #trade-date-select:focus {
    outline: none;
    border-color: #6366f1;
}

.search-input::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.search-btn, .filter-btn, .clear-btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    background: #6366f1;
    color: white;
    font-size: 14px;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.search-btn:hover, .filter-btn:hover {
    background: #5b5bd6;
}

.clear-btn {
    background: #ef4444;
}

.clear-btn:hover {
    background: #dc2626;
}

.filter-select option, #trade-date-select option {
    background: #1e293b;
    color: white;
}

/* 简化的表格样式 */
.table-container {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    /* 优化滚动条样式 */
    max-height: 65vh; /* 减少最大高度，为水平滚动条留出空间 */
    overflow-y: auto; /* 只保留垂直滚动，水平滚动由内部wrapper处理 */
    position: relative; /* 为固定表头做准备 */
}

/* 自定义滚动条样式 */
.table-container::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

.table-container::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}

.table-container::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 4px;
}

.table-container::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}

/* 表格包装器样式 */
.table-wrapper {
    overflow-x: auto;
    overflow-y: visible;
    border-radius: 6px;
    /* 让水平滚动条更明显 */
    scrollbar-width: thin;
    scrollbar-color: rgba(255, 255, 255, 0.3) rgba(255, 255, 255, 0.1);
    /* 确保水平滚动条始终可见 */
    min-height: 400px; /* 设置最小高度 */
    position: relative;
    /* 添加底部边距为滚动条留出空间 */
    margin-bottom: 20px;
}

.table-wrapper::-webkit-scrollbar {
    height: 16px; /* 进一步增加水平滚动条高度，使其更明显 */
    width: 16px; /* 也设置垂直滚动条宽度 */
}

.table-wrapper::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.15);
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.table-wrapper::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.5);
    border-radius: 8px;
    border: 2px solid rgba(255, 255, 255, 0.1);
    /* 添加渐变效果使其更明显 */
    background: linear-gradient(45deg, rgba(255, 255, 255, 0.4), rgba(255, 255, 255, 0.6));
}

.table-wrapper::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(45deg, rgba(255, 255, 255, 0.6), rgba(255, 255, 255, 0.8));
    border-color: rgba(255, 255, 255, 0.2);
}

.table-wrapper::-webkit-scrollbar-corner {
    background: rgba(255, 255, 255, 0.1);
}

/* 滚动提示样式 - 改进版 */
.scroll-hint {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.9), rgba(147, 51, 234, 0.9));
    color: white;
    padding: 12px 20px;
    border-radius: 8px;
    margin-bottom: 15px;
    text-align: center;
    font-size: 14px;
    font-weight: 500;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.2);
    animation: slideInDown 0.5s ease-out;
}

@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 表格固定表头样式 */
.simple-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 13px;
    background: rgba(255, 255, 255, 0.02);
    border-radius: 6px;
    overflow: hidden;
    /* 使表格支持固定表头 */
    position: relative;
}

.simple-table thead {
    position: sticky;
    top: 0;
    z-index: 10;
    background: rgba(30, 41, 59, 0.95);
    backdrop-filter: blur(10px);
}

.simple-table thead th {
    background: rgba(30, 41, 59, 0.95);
    color: #e2e8f0;
    font-weight: 600;
    padding: 12px 8px;
    text-align: left;
    border-bottom: 2px solid rgba(255, 255, 255, 0.1);
    white-space: nowrap;
    font-size: 12px;
    /* 添加阴影使固定表头更明显 */
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 原有的滚动提示样式保持不变 */
.scroll-hint-old {
    background: rgba(59, 130, 246, 0.1);
    border: 1px solid rgba(59, 130, 246, 0.3);
    border-radius: 6px;
    padding: 8px 12px;
    margin-bottom: 10px;
    text-align: center;
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.8);
    animation: fadeInOut 3s ease-in-out;
}

@keyframes fadeInOut {
    0% { opacity: 0; }
    20% { opacity: 1; }
    80% { opacity: 1; }
    100% { opacity: 0; }
}

.simple-table {
    width: 100%;
    border-collapse: collapse;
    min-width: 1200px; /* 确保表格有足够的最小宽度 */
}

/* 粘性表头 */
.simple-table thead th {
    position: sticky;
    top: 0;
    background: rgba(30, 41, 59, 0.95);
    backdrop-filter: blur(10px);
    z-index: 10;
    border-bottom: 2px solid rgba(255, 255, 255, 0.2);
}
    background: transparent;
    font-size: 13px;
    color: white;
}

.simple-table th {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    padding: 10px 8px;
    text-align: left;
    font-weight: 600;
    border-bottom: 2px solid rgba(255, 255, 255, 0.2);
    cursor: pointer;
    user-select: none;
    transition: background-color 0.2s ease;
    white-space: nowrap;
}

.simple-table th:hover {
    background: rgba(255, 255, 255, 0.15);
}

.simple-table th.sortable::after {
    content: ' ↕';
    font-size: 12px;
    opacity: 0.6;
}

.simple-table th.sort-asc::after {
    content: ' ↑';
    color: #ffd700;
}

.simple-table th.sort-desc::after {
    content: ' ↓';
    color: #ffd700;
}

.simple-table td {
    padding: 8px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    transition: background-color 0.2s ease;
    white-space: nowrap;
}

.simple-table tbody tr:hover {
    background: rgba(255, 255, 255, 0.05);
}

.simple-table tbody tr:nth-child(even) {
    background: rgba(255, 255, 255, 0.02);
}

.simple-table tbody tr:nth-child(even):hover {
    background: rgba(255, 255, 255, 0.07);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .controls-container {
        flex-direction: column;
        align-items: stretch;
    }

    .date-controls, .search-group, .filter-group {
        width: 100%;
        justify-content: space-between;
    }

    .simple-table {
        font-size: 11px;
    }

    .simple-table th,
    .simple-table td {
        padding: 6px 4px;
    }
}
