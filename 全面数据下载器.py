#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全面Tushare数据下载器
下载所有可用的股票数据，包括PE、PB、股息率等财务因子
"""

import json
import tushare as ts
from datetime import datetime
from 数据下载器 import Tushare数据下载器

def 全面下载数据():
    """全面下载所有可用的Tushare数据"""
    print("🚀 启动全面数据下载器")
    print("📊 将下载所有可用的Tushare数据，包括PE、PB、股息率等财务因子")
    print("⏰ 预计需要较长时间，请耐心等待...")
    print("💡 下载过程中可以按 Ctrl+C 暂停，支持断点续传")
    print("=" * 60)

    try:
        # 读取配置
        with open('配置文件.json', 'r', encoding='utf-8') as f:
            配置 = json.load(f)

        # 初始化tushare
        ts.set_token(配置['tushare配置']['api_token'])

        # 创建下载器实例
        下载器 = Tushare数据下载器()
        下载器.下载统计["开始时间"] = datetime.now()

        print("📥 开始全面数据下载...")

        # 第一阶段：基础数据
        print("\n🔹 第一阶段：基础数据下载")
        print("1️⃣ 下载股票基础信息...")
        下载器.下载股票基础信息()

        print("2️⃣ 下载交易日历...")
        下载器.下载交易日历(2020, 2025)

        # 第二阶段：行情数据
        print("\n🔹 第二阶段：行情数据下载")
        股票列表 = 下载器.获取股票列表()
        print(f"📊 共需下载 {len(股票列表)} 只股票的数据")

        print("3️⃣ 下载日线行情数据...")
        下载器.下载日线行情("20200101", None, 股票列表)

        print("4️⃣ 下载复权因子...")
        下载器.下载复权因子("20200101", None, 股票列表)

        # 第三阶段：财务指标数据（重点）
        print("\n🔹 第三阶段：财务指标数据下载（包含PE、PB、股息率等）")
        print("5️⃣ 下载每日指标（PE、PB、PS、股息率、市值等）...")
        下载器.下载每日指标("20200101", None)

        print("6️⃣ 下载分红送股数据（用于计算股息率）...")
        下载器.下载分红送股(2020, 2025)

        print("7️⃣ 下载财务报表数据（利润表、资产负债表、现金流量表）...")
        下载器.下载财务数据(2020, 2024)

        # 第四阶段：其他重要数据
        print("\n🔹 第四阶段：其他重要数据下载")
        
        # 下载公司基础信息
        print("8️⃣ 下载上市公司基础信息...")
        try:
            任务键 = "stock_company_all"
            if 下载器.下载进度.get(任务键, {}).get("状态") != "完成":
                下载器.下载进度[任务键] = {"状态": "进行中", "开始时间": datetime.now().isoformat()}
                下载器._保存下载进度()

                数据 = 下载器._调用API("stock_company")
                if 数据 is not None and not 数据.empty:
                    下载器.db.插入数据("上市公司信息", 数据)
                    下载器.下载进度[任务键] = {
                        "状态": "完成",
                        "完成时间": datetime.now().isoformat(),
                        "数据条数": len(数据)
                    }
                    下载器._保存下载进度()
                    print(f"   ✅ 上市公司信息下载完成，共 {len(数据)} 条")
        except Exception as e:
            print(f"   ❌ 上市公司信息下载失败: {e}")

        # 下载涨跌停数据
        print("9️⃣ 下载涨跌停价格数据...")
        try:
            # 获取最近30个交易日
            交易日历 = 下载器.db.查询数据("""
                SELECT cal_date FROM 交易日历 
                WHERE is_open = 1 AND cal_date >= '20240701'
                ORDER BY cal_date DESC LIMIT 30
            """)
            
            for _, row in 交易日历.iterrows():
                交易日期 = row['cal_date']
                任务键 = f"stk_limit_{交易日期}"
                
                if 下载器.下载进度.get(任务键, {}).get("状态") == "完成":
                    continue
                
                try:
                    下载器.下载进度[任务键] = {"状态": "进行中", "开始时间": datetime.now().isoformat()}
                    下载器._保存下载进度()
                    
                    数据 = 下载器._调用API("stk_limit", trade_date=交易日期)
                    if 数据 is not None and not 数据.empty:
                        下载器.db.插入数据("涨跌停价格", 数据)
                        下载器.下载进度[任务键] = {
                            "状态": "完成", 
                            "完成时间": datetime.now().isoformat(),
                            "数据条数": len(数据)
                        }
                        下载器._保存下载进度()
                        print(f"   ✅ {交易日期} 涨跌停数据下载完成，共 {len(数据)} 条")
                except Exception as e:
                    print(f"   ❌ {交易日期} 涨跌停数据下载失败: {e}")
                    
        except Exception as e:
            print(f"   ❌ 涨跌停数据下载失败: {e}")

        print("\n✅ 全面数据下载完成！")
        
        # 显示下载统计
        下载器.下载统计["结束时间"] = datetime.now()
        下载器.显示下载统计()
        
        # 显示数据概览
        print("\n📊 数据概览：")
        表信息 = 下载器.db.获取所有表信息()
        总记录数 = 0
        for 表名, 信息 in 表信息.items():
            if 表名 != "下载进度记录":
                记录数 = 信息.get('记录数', 0)
                总记录数 += 记录数
                print(f"  📋 {表名}: {记录数:,} 条记录")
        
        print(f"\n🎉 总计下载数据: {总记录数:,} 条记录")
        print("🌐 现在可以启动Web界面查看数据了！")

    except KeyboardInterrupt:
        print("\n⏸️ 用户中断下载")
        print("💾 下载进度已保存，下次启动将从断点继续")
    except Exception as e:
        print(f"❌ 下载过程出错: {e}")
    finally:
        if '下载器' in locals():
            下载器.下载统计["结束时间"] = datetime.now()
            下载器.显示下载统计()
            下载器.db.关闭连接()

def 显示可下载数据类型():
    """显示所有可下载的数据类型"""
    print("📋 可下载的数据类型：")
    print("=" * 60)
    
    数据类型 = {
        "🏢 基础数据": [
            "股票基础信息 - 股票代码、名称、行业、地区等",
            "交易日历 - 交易日期、是否开市",
            "上市公司信息 - 公司详细信息、管理层等"
        ],
        "📈 行情数据": [
            "日线行情 - 开高低收、成交量、成交额",
            "复权因子 - 用于价格复权计算",
            "涨跌停价格 - 每日涨跌停限制价格"
        ],
        "💰 财务指标": [
            "每日指标 - PE、PB、PS、股息率、市值、换手率等",
            "市盈率(PE) - 价格收益比",
            "市净率(PB) - 价格净资产比",
            "市销率(PS) - 价格销售收入比",
            "股息率 - 分红收益率",
            "总市值、流通市值",
            "换手率、量比等"
        ],
        "📊 财务报表": [
            "利润表 - 营业收入、净利润、每股收益等",
            "资产负债表 - 总资产、净资产、负债等",
            "现金流量表 - 经营现金流、投资现金流等",
            "分红送股 - 分红派息、送股转股记录"
        ],
        "📊 市场数据": [
            "融资融券 - 融资融券交易数据",
            "龙虎榜 - 大额交易明细",
            "股东信息 - 前十大股东持股情况"
        ]
    }
    
    for 分类, 数据列表 in 数据类型.items():
        print(f"\n{分类}")
        print("-" * 40)
        for 数据描述 in 数据列表:
            print(f"  • {数据描述}")
    
    print("\n" + "=" * 60)
    print("💡 重点：每日指标包含了PE、PB、股息率等所有重要财务因子！")

def main():
    """主函数"""
    print("🎯 Tushare全面数据下载器")
    print("=" * 60)
    
    while True:
        print("\n请选择操作：")
        print("1. 查看可下载的数据类型")
        print("2. 开始全面数据下载")
        print("0. 退出")
        
        选择 = input("\n请输入选择 (0-2): ").strip()
        
        if 选择 == "1":
            显示可下载数据类型()
        elif 选择 == "2":
            确认 = input("\n⚠️  全面下载将花费较长时间，确认开始？(y/N): ").strip().lower()
            if 确认 in ['y', 'yes', '是']:
                全面下载数据()
                break
            else:
                print("❌ 已取消下载")
        elif 选择 == "0":
            print("👋 再见！")
            break
        else:
            print("❌ 无效选择，请重新输入")

if __name__ == "__main__":
    main()
