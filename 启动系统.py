#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Tushare股票数据下载系统 - 统一启动入口
作者：AI助手
创建时间：2025-01-01
"""

import os
import sys
import time
import subprocess
import threading
from pathlib import Path

def 显示菜单():
    """显示主菜单"""
    print("\n" + "="*60)
    print("🚀 Tushare股票数据下载系统")
    print("="*60)
    print("1. 📊 启动Web可视化界面（推荐）")
    print("2. 📥 开始下载股票数据（基础下载）")
    print("3. 🎯 全面数据下载（包含PE、PB、股息率等财务因子）")
    print("4. 🔍 查看数据库状态")
    print("5. ⚙️  查看系统配置")
    print("6. 📖 查看使用说明")
    print("0. 🚪 退出系统")
    print("="*60)

def 启动Web界面():
    """启动Web可视化界面"""
    print("\n🌐 正在启动Web可视化界面...")
    print("📍 访问地址：http://127.0.0.1:5000")
    print("💡 提示：启动后会自动打开浏览器")
    print("⚠️  按 Ctrl+C 可停止服务")

    try:
        # 启动Web服务
        from 网页数据接口 import 网页数据接口
        import webbrowser

        # 创建Web接口实例
        web_api = 网页数据接口()

        # 在新线程中打开浏览器
        def 打开浏览器():
            time.sleep(2)  # 等待服务器启动
            webbrowser.open('http://127.0.0.1:5000')

        threading.Thread(target=打开浏览器, daemon=True).start()

        # 启动Web服务器
        web_api.启动服务器()

    except KeyboardInterrupt:
        print("\n✅ Web服务已停止")
    except Exception as e:
        print(f"❌ 启动Web界面失败: {e}")

def 开始下载数据():
    """开始下载股票数据"""
    print("\n📥 正在启动数据下载...")
    print("💡 提示：下载过程中可以按 Ctrl+C 暂停")
    print("📊 下载进度会实时显示")

    try:
        import json
        import tushare as ts
        from datetime import datetime
        from 数据下载器 import Tushare数据下载器

        # 读取配置
        with open('配置文件.json', 'r', encoding='utf-8') as f:
            配置 = json.load(f)

        # 初始化tushare
        ts.set_token(配置['tushare配置']['api_token'])

        # 创建下载器实例
        下载器 = Tushare数据下载器()
        下载器.下载统计["开始时间"] = datetime.now()

        print("📥 开始下载股票数据...")

        # 下载基础数据
        print("1️⃣ 下载股票基础信息...")
        下载器.下载股票基础信息()

        print("2️⃣ 下载交易日历...")
        下载器.下载交易日历(2020, 2025)

        print("3️⃣ 下载日线行情数据...")
        # 获取所有股票列表
        股票列表 = 下载器.获取股票列表()
        print(f"📊 共需下载 {len(股票列表)} 只股票的数据")

        # 下载所有股票的日线行情
        下载器.下载日线行情("20240101", None, 股票列表)

        print("✅ 数据下载完成！")

    except KeyboardInterrupt:
        print("\n⏸️  数据下载已暂停")
        print("💡 下次启动会从断点继续下载")
    except Exception as e:
        print(f"❌ 数据下载失败: {e}")
        import traceback
        traceback.print_exc()

def 全面下载数据():
    """启动全面数据下载"""
    print("\n🎯 启动全面数据下载器...")
    print("📊 将下载所有可用的Tushare数据，包括PE、PB、股息率等财务因子")
    print("⏰ 预计需要较长时间，请耐心等待...")

    确认 = input("\n⚠️  全面下载将花费较长时间，确认开始？(y/N): ").strip().lower()
    if 确认 not in ['y', 'yes', '是']:
        print("❌ 已取消下载")
        return

    try:
        # 导入全面下载器
        from 全面数据下载器 import 全面下载数据 as 执行全面下载
        执行全面下载()
    except ImportError as e:
        print(f"❌ 导入全面下载器失败: {e}")
        print("💡 请确保 全面数据下载器.py 文件存在")
    except Exception as e:
        print(f"❌ 全面下载失败: {e}")

def 查看数据库状态():
    """查看数据库状态"""
    print("\n🔍 正在查询数据库状态...")
    
    try:
        from 数据库管理器 import 数据库管理器
        
        db = 数据库管理器()
        
        # 查询股票基础信息
        股票总数_sql = "SELECT COUNT(*) as count FROM 股票基础信息"
        股票总数_结果 = db.查询数据(股票总数_sql)
        股票总数 = 股票总数_结果.iloc[0]['count'] if not 股票总数_结果.empty else 0
        
        # 查询日线行情数据
        日线数据_sql = "SELECT COUNT(*) as count FROM 日线行情"
        日线数据_结果 = db.查询数据(日线数据_sql)
        日线数据总数 = 日线数据_结果.iloc[0]['count'] if not 日线数据_结果.empty else 0
        
        # 查询最新交易日
        最新日期_sql = "SELECT MAX(trade_date) as latest_date FROM 日线行情"
        最新日期_结果 = db.查询数据(最新日期_sql)
        最新交易日 = 最新日期_结果.iloc[0]['latest_date'] if not 最新日期_结果.empty else "无数据"
        
        # 查询有数据的股票数量
        有数据股票_sql = """
            SELECT COUNT(DISTINCT ts_code) as count 
            FROM 日线行情 
            WHERE trade_date = (SELECT MAX(trade_date) FROM 日线行情)
        """
        有数据股票_结果 = db.查询数据(有数据股票_sql)
        有数据股票数 = 有数据股票_结果.iloc[0]['count'] if not 有数据股票_结果.empty else 0
        
        print(f"\n📊 数据库状态报告")
        print(f"├─ 股票总数：{股票总数:,} 只")
        print(f"├─ 日线数据：{日线数据总数:,} 条")
        print(f"├─ 最新交易日：{最新交易日}")
        print(f"└─ 有数据股票：{有数据股票数:,} 只")
        
        if 股票总数 > 0:
            覆盖率 = (有数据股票数 / 股票总数) * 100
            print(f"\n📈 数据覆盖率：{覆盖率:.1f}%")
        
        db.关闭连接()
        
    except Exception as e:
        print(f"❌ 查询数据库状态失败: {e}")

def 查看系统配置():
    """查看系统配置"""
    print("\n⚙️  系统配置信息")

    try:
        import json
        with open('配置文件.json', 'r', encoding='utf-8') as f:
            配置 = json.load(f)

        print(f"├─ API Token：{配置['tushare配置']['api_token'][:10]}...（已隐藏）")
        print(f"├─ 数据库文件：{配置['数据库配置']['数据库文件']}")
        print(f"├─ 下载间隔：{配置['tushare配置']['调用间隔秒数']} 秒")
        print(f"├─ 批量大小：{配置['下载配置']['批量下载大小']} 只股票")
        print(f"└─ Web端口：{配置['Web服务器配置']['端口']}")

    except Exception as e:
        print(f"❌ 读取配置文件失败: {e}")
        import traceback
        traceback.print_exc()

def 查看使用说明():
    """查看使用说明"""
    print("\n📖 使用说明")
    
    try:
        if os.path.exists('使用说明.md'):
            with open('使用说明.md', 'r', encoding='utf-8') as f:
                内容 = f.read()
            print(内容)
        else:
            print("❌ 使用说明文件不存在")
    except Exception as e:
        print(f"❌ 读取使用说明失败: {e}")

def main():
    """主函数"""
    print("🎉 欢迎使用 Tushare股票数据下载系统！")

    while True:
        try:
            显示菜单()
            选择 = input("\n请选择操作 (0-6): ").strip()

            if 选择 == "1":
                启动Web界面()
            elif 选择 == "2":
                开始下载数据()
            elif 选择 == "3":
                全面下载数据()
            elif 选择 == "4":
                查看数据库状态()
            elif 选择 == "5":
                查看系统配置()
            elif 选择 == "6":
                查看使用说明()
            elif 选择 == "0":
                print("\n👋 感谢使用，再见！")
                break
            else:
                print("\n❌ 无效选择，请输入 0-6 之间的数字")

        except KeyboardInterrupt:
            print("\n\n👋 用户中断，再见！")
            break
        except Exception as e:
            print(f"\n❌ 发生错误: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    main()
