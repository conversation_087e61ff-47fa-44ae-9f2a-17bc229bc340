#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Tushare数据下载系统 - 快速启动脚本
简化的启动界面，提供最常用的功能
"""

import os
import sys
import subprocess
import time
from datetime import datetime

def 启动Web界面():
    """启动Web可视化界面"""
    print("🚀 启动Web可视化界面...")
    print("📊 界面地址: http://localhost:5000")
    print("💡 包含完整的PE、PB、股息率等财务指标数据")
    print("⏰ 启动中，请稍候...")
    
    try:
        # 启动Web服务器
        subprocess.Popen([sys.executable, "网页数据接口.py"], 
                        cwd=os.getcwd(),
                        creationflags=subprocess.CREATE_NEW_CONSOLE if os.name == 'nt' else 0)
        
        # 等待服务器启动
        time.sleep(3)
        
        # 打开浏览器
        import webbrowser
        webbrowser.open("http://localhost:5000")
        
        print("✅ Web界面已启动！")
        print("🌐 浏览器应该已自动打开，如果没有请手动访问: http://localhost:5000")
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")

def 下载财务指标():
    """下载最新的财务指标数据"""
    print("📊 下载最新财务指标数据...")
    print("💰 包含PE、PB、PS、股息率、市值等指标")
    
    确认 = input("⚠️  下载将消耗API调用次数，确认开始？(y/N): ").strip().lower()
    if 确认 not in ['y', 'yes', '是']:
        print("❌ 已取消下载")
        return
    
    try:
        from 数据下载器 import Tushare数据下载器
        from datetime import datetime, timedelta
        
        下载器 = Tushare数据下载器()
        
        # 下载最近30天的财务指标
        结束日期 = datetime.now().strftime("%Y%m%d")
        开始日期 = (datetime.now() - timedelta(days=30)).strftime("%Y%m%d")
        
        print(f"📅 下载日期范围: {开始日期} - {结束日期}")
        下载器.下载每日指标(开始日期, 结束日期)
        
        print("✅ 财务指标下载完成！")
        print("💡 现在可以在Web界面中查看PE、PB、股息率等数据")
        
    except Exception as e:
        print(f"❌ 下载失败: {e}")

def 查看数据库状态():
    """查看数据库状态"""
    print("🔍 查看数据库状态...")
    
    try:
        from 数据库管理器 import 数据库管理器
        
        db = 数据库管理器()
        
        # 查询各表的数据量
        表列表 = [
            ("股票基础信息", "股票基础信息"),
            ("日线行情", "日线行情"),
            ("每日指标", "每日指标"),
            ("交易日历", "交易日历")
        ]
        
        print("\n📊 数据库状态:")
        print("-" * 50)
        
        for 表名, 显示名 in 表列表:
            try:
                查询SQL = f"SELECT COUNT(*) as count FROM {表名}"
                结果 = db.查询数据(查询SQL)
                数量 = 结果.iloc[0]['count'] if not 结果.empty else 0
                print(f"{显示名:12}: {数量:>10,} 条记录")
            except:
                print(f"{显示名:12}: {'表不存在':>10}")
        
        # 检查财务指标数据
        try:
            财务指标SQL = """
            SELECT COUNT(DISTINCT ts_code) as stock_count,
                   COUNT(*) as total_records,
                   MAX(trade_date) as latest_date
            FROM 每日指标
            WHERE pe IS NOT NULL
            """
            财务结果 = db.查询数据(财务指标SQL)
            
            if not 财务结果.empty:
                股票数 = 财务结果.iloc[0]['stock_count']
                记录数 = 财务结果.iloc[0]['total_records']
                最新日期 = 财务结果.iloc[0]['latest_date']
                
                print("\n💰 财务指标数据:")
                print("-" * 50)
                print(f"有PE数据的股票: {股票数:>10,} 只")
                print(f"财务指标记录数: {记录数:>10,} 条")
                print(f"最新数据日期  : {最新日期:>10}")
        except:
            print("\n💰 财务指标数据: 暂无数据")
        
        print("-" * 50)
        
    except Exception as e:
        print(f"❌ 查询失败: {e}")

def main():
    """主函数"""
    print("🎉 Tushare数据下载系统 - 快速启动")
    print("=" * 60)
    print("💡 系统已包含完整的PE、PB、股息率等财务指标功能")
    print("=" * 60)
    
    while True:
        print("\n请选择操作:")
        print("1. 🌐 启动Web可视化界面（推荐）")
        print("2. 📊 下载最新财务指标数据")
        print("3. 🔍 查看数据库状态")
        print("4. 📖 查看使用说明")
        print("0. 🚪 退出")
        print("-" * 40)
        
        选择 = input("请输入选择 (0-4): ").strip()
        
        if 选择 == "1":
            启动Web界面()
        elif 选择 == "2":
            下载财务指标()
        elif 选择 == "3":
            查看数据库状态()
        elif 选择 == "4":
            print("\n📖 使用说明:")
            print("=" * 40)
            print("1. 首次使用建议先选择'下载最新财务指标数据'")
            print("2. 然后选择'启动Web可视化界面'查看数据")
            print("3. Web界面包含以下功能:")
            print("   • 📊 市场概览 - 整体市场情况")
            print("   • 📈 股票图表 - 个股K线图")
            print("   • 🗄️  数据库状态 - 数据统计")
            print("   • 📋 数据详情 - 完整股票数据表格")
            print("4. 数据详情页面包含以下财务指标:")
            print("   • PE/PE_TTM - 市盈率")
            print("   • PB - 市净率")
            print("   • PS/PS_TTM - 市销率")
            print("   • 股息率 - 分红收益率")
            print("   • 总市值/流通市值")
            print("   • 换手率、量比等")
            print("=" * 40)
        elif 选择 == "0":
            print("👋 再见！")
            break
        else:
            print("❌ 无效选择，请重新输入")

if __name__ == "__main__":
    main()
