#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Tushare数据下载器
支持断点续传、频率控制和数据完整性保证
"""

import tushare as ts
import pandas as pd
import json
import time
import logging
from datetime import datetime, timedelta
import os
import threading
from 频率控制器 import 全局频率控制器, 频率控制
from 数据库管理器 import 数据库管理器

class Tushare数据下载器:
    """Tushare数据下载器主类"""
    
    def __init__(self, 配置文件路径="配置文件.json"):
        """
        初始化数据下载器
        
        参数:
            配置文件路径 (str): 配置文件路径
        """
        # 加载配置
        self.配置 = self._加载配置(配置文件路径)
        
        # 初始化tushare
        token = self.配置["tushare配置"]["api_token"]
        ts.set_token(token)
        self.pro = ts.pro_api()
        
        # 初始化数据库
        数据库文件 = self.配置["数据库配置"]["数据库文件"]
        self.db = 数据库管理器(数据库文件)
        
        # 初始化频率控制器
        self.频率控制器 = 全局频率控制器
        
        # 设置日志
        self._设置日志()
        
        # 加载权限信息
        self.权限信息 = self._加载权限信息()
        
        # 断点续传文件
        self.进度文件 = self.配置["下载配置"]["断点续传文件"]
        self.下载进度 = self._加载下载进度()
        
        # 下载统计
        self.下载统计 = {
            "总调用次数": 0,
            "成功次数": 0,
            "失败次数": 0,
            "下载数据条数": 0,
            "开始时间": None,
            "结束时间": None
        }
        
        self.logger.info("数据下载器初始化完成")
    
    def _加载配置(self, 配置文件路径):
        """加载配置文件"""
        try:
            with open(配置文件路径, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            raise
    
    def _设置日志(self):
        """设置日志配置"""
        日志配置 = self.配置["日志配置"]
        
        logging.basicConfig(
            level=getattr(logging, 日志配置["日志级别"]),
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(日志配置["日志文件"], encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def _加载权限信息(self):
        """加载权限信息"""
        try:
            with open("详细权限信息.json", 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            self.logger.warning(f"加载权限信息失败: {e}")
            return {}
    
    def _加载下载进度(self):
        """加载下载进度"""
        try:
            if os.path.exists(self.进度文件):
                with open(self.进度文件, 'r', encoding='utf-8') as f:
                    进度数据 = json.load(f)
                    self.logger.info(f"加载下载进度: {len(进度数据)} 个任务")
                    return 进度数据
        except Exception as e:
            self.logger.warning(f"加载下载进度失败: {e}")
        
        return {}
    
    def _保存下载进度(self):
        """保存下载进度"""
        try:
            with open(self.进度文件, 'w', encoding='utf-8') as f:
                json.dump(self.下载进度, f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.logger.error(f"保存下载进度失败: {e}")
    
    @频率控制
    def _调用API(self, 接口名称, **参数):
        """
        调用Tushare API（带频率控制）
        
        参数:
            接口名称 (str): API接口名称
            **参数: API调用参数
            
        返回:
            pd.DataFrame: API返回的数据
        """
        self.下载统计["总调用次数"] += 1
        
        try:
            # 获取API函数
            api_func = getattr(self.pro, 接口名称)
            
            # 调用API
            self.logger.debug(f"调用API: {接口名称}, 参数: {参数}")
            结果 = api_func(**参数)
            
            self.下载统计["成功次数"] += 1
            if 结果 is not None:
                self.下载统计["下载数据条数"] += len(结果)
            
            return 结果
            
        except Exception as e:
            self.下载统计["失败次数"] += 1
            self.logger.error(f"API调用失败 {接口名称}: {e}")
            raise
    
    def 下载股票基础信息(self):
        """下载股票基础信息"""
        接口名称 = "stock_basic"
        任务键 = f"{接口名称}_all"
        
        if self.下载进度.get(任务键, {}).get("状态") == "完成":
            self.logger.info(f"任务 {任务键} 已完成，跳过")
            return
        
        self.logger.info("开始下载股票基础信息...")
        
        try:
            # 记录开始
            self.db.记录下载进度(接口名称, {}, "开始")
            self.下载进度[任务键] = {"状态": "进行中", "开始时间": datetime.now().isoformat()}
            self._保存下载进度()
            
            # 下载数据
            数据 = self._调用API(接口名称, list_status='L')
            
            if 数据 is not None and not 数据.empty:
                # 保存到数据库
                self.db.插入数据("股票基础信息", 数据)
                
                # 记录完成
                self.db.记录下载进度(接口名称, {}, "完成", len(数据))
                self.下载进度[任务键] = {
                    "状态": "完成", 
                    "完成时间": datetime.now().isoformat(),
                    "数据条数": len(数据)
                }
                self._保存下载进度()
                
                self.logger.info(f"股票基础信息下载完成，共 {len(数据)} 条")
            else:
                self.logger.warning("股票基础信息下载结果为空")
                
        except Exception as e:
            self.logger.error(f"下载股票基础信息失败: {e}")
            self.db.记录下载进度(接口名称, {}, "失败", 0, str(e))
            self.下载进度[任务键] = {
                "状态": "失败", 
                "错误时间": datetime.now().isoformat(),
                "错误信息": str(e)
            }
            self._保存下载进度()
    
    def 下载交易日历(self, 开始年份=2020, 结束年份=None):
        """
        下载交易日历
        
        参数:
            开始年份 (int): 开始年份
            结束年份 (int): 结束年份，默认为当前年份
        """
        if 结束年份 is None:
            结束年份 = datetime.now().year
        
        接口名称 = "trade_cal"
        
        for 年份 in range(开始年份, 结束年份 + 1):
            任务键 = f"{接口名称}_{年份}"
            
            if self.下载进度.get(任务键, {}).get("状态") == "完成":
                self.logger.info(f"任务 {任务键} 已完成，跳过")
                continue
            
            self.logger.info(f"下载 {年份} 年交易日历...")
            
            try:
                开始日期 = f"{年份}0101"
                结束日期 = f"{年份}1231"
                参数 = {"start_date": 开始日期, "end_date": 结束日期}
                
                # 记录开始
                self.db.记录下载进度(接口名称, 参数, "开始")
                self.下载进度[任务键] = {"状态": "进行中", "开始时间": datetime.now().isoformat()}
                self._保存下载进度()
                
                # 下载数据
                数据 = self._调用API(接口名称, **参数)
                
                if 数据 is not None and not 数据.empty:
                    # 保存到数据库
                    self.db.插入数据("交易日历", 数据)
                    
                    # 记录完成
                    self.db.记录下载进度(接口名称, 参数, "完成", len(数据))
                    self.下载进度[任务键] = {
                        "状态": "完成", 
                        "完成时间": datetime.now().isoformat(),
                        "数据条数": len(数据)
                    }
                    self._保存下载进度()
                    
                    self.logger.info(f"{年份} 年交易日历下载完成，共 {len(数据)} 条")
                else:
                    self.logger.warning(f"{年份} 年交易日历下载结果为空")
                    
            except Exception as e:
                self.logger.error(f"下载 {年份} 年交易日历失败: {e}")
                self.db.记录下载进度(接口名称, 参数, "失败", 0, str(e))
                self.下载进度[任务键] = {
                    "状态": "失败", 
                    "错误时间": datetime.now().isoformat(),
                    "错误信息": str(e)
                }
                self._保存下载进度()
    
    def 获取股票列表(self):
        """获取股票列表"""
        try:
            股票列表 = self.db.查询数据("SELECT ts_code FROM 股票基础信息")
            return 股票列表['ts_code'].tolist()
        except:
            self.logger.warning("从数据库获取股票列表失败，使用API获取")
            数据 = self._调用API("stock_basic", list_status='L')
            return 数据['ts_code'].tolist() if 数据 is not None else []
    
    def 下载日线行情(self, 开始日期=None, 结束日期=None, 股票代码列表=None):
        """
        下载日线行情数据

        参数:
            开始日期 (str): 开始日期，格式YYYYMMDD
            结束日期 (str): 结束日期，格式YYYYMMDD
            股票代码列表 (list): 股票代码列表，为None时下载所有股票
        """
        if 开始日期 is None:
            开始日期 = "20200101"
        if 结束日期 is None:
            结束日期 = datetime.now().strftime("%Y%m%d")

        if 股票代码列表 is None:
            股票代码列表 = self.获取股票列表()

        接口名称 = "daily"
        总股票数 = len(股票代码列表)

        self.logger.info(f"开始下载日线行情，股票数量: {总股票数}, 日期范围: {开始日期} - {结束日期}")

        for i, 股票代码 in enumerate(股票代码列表):
            任务键 = f"{接口名称}_{股票代码}_{开始日期}_{结束日期}"

            if self.下载进度.get(任务键, {}).get("状态") == "完成":
                continue

            self.logger.info(f"下载 {股票代码} 日线行情 ({i+1}/{总股票数})")

            try:
                参数 = {
                    "ts_code": 股票代码,
                    "start_date": 开始日期,
                    "end_date": 结束日期
                }

                # 记录开始
                self.下载进度[任务键] = {"状态": "进行中", "开始时间": datetime.now().isoformat()}
                self._保存下载进度()

                # 下载数据
                数据 = self._调用API(接口名称, **参数)

                if 数据 is not None and not 数据.empty:
                    # 保存到数据库
                    self.db.插入数据("日线行情", 数据)

                    # 记录完成
                    self.下载进度[任务键] = {
                        "状态": "完成",
                        "完成时间": datetime.now().isoformat(),
                        "数据条数": len(数据)
                    }
                    self._保存下载进度()

                    self.logger.debug(f"{股票代码} 日线行情下载完成，共 {len(数据)} 条")
                else:
                    self.logger.debug(f"{股票代码} 日线行情无数据")
                    self.下载进度[任务键] = {
                        "状态": "完成",
                        "完成时间": datetime.now().isoformat(),
                        "数据条数": 0
                    }
                    self._保存下载进度()

            except Exception as e:
                self.logger.error(f"下载 {股票代码} 日线行情失败: {e}")
                self.下载进度[任务键] = {
                    "状态": "失败",
                    "错误时间": datetime.now().isoformat(),
                    "错误信息": str(e)
                }
                self._保存下载进度()

    def 下载每日指标(self, 开始日期=None, 结束日期=None):
        """
        下载每日指标数据（包含PE、PB、PS、股息率等财务指标）

        参数:
            开始日期 (str): 开始日期，格式YYYYMMDD
            结束日期 (str): 结束日期，格式YYYYMMDD
        """
        if 开始日期 is None:
            开始日期 = "20200101"
        if 结束日期 is None:
            结束日期 = datetime.now().strftime("%Y%m%d")

        接口名称 = "daily_basic"

        # 获取交易日历
        交易日历 = self.db.查询数据("""
            SELECT cal_date FROM 交易日历
            WHERE cal_date >= ? AND cal_date <= ? AND is_open = 1
            ORDER BY cal_date
        """, (开始日期, 结束日期))

        if 交易日历.empty:
            self.logger.warning("没有找到交易日历数据，请先下载交易日历")
            return

        交易日期列表 = 交易日历['cal_date'].tolist()
        总日期数 = len(交易日期列表)

        self.logger.info(f"开始下载每日指标，交易日期数量: {总日期数}, 日期范围: {开始日期} - {结束日期}")

        for i, 交易日期 in enumerate(交易日期列表):
            任务键 = f"{接口名称}_{交易日期}"

            if self.下载进度.get(任务键, {}).get("状态") == "完成":
                continue

            self.logger.info(f"下载 {交易日期} 每日指标 ({i+1}/{总日期数})")

            try:
                参数 = {"trade_date": 交易日期}

                # 记录开始
                self.下载进度[任务键] = {"状态": "进行中", "开始时间": datetime.now().isoformat()}
                self._保存下载进度()

                # 下载数据
                数据 = self._调用API(接口名称, **参数)

                if 数据 is not None and not 数据.empty:
                    # 保存到数据库
                    self.db.插入数据("每日指标", 数据)

                    # 记录完成
                    self.下载进度[任务键] = {
                        "状态": "完成",
                        "完成时间": datetime.now().isoformat(),
                        "数据条数": len(数据)
                    }
                    self._保存下载进度()

                    self.logger.debug(f"{交易日期} 每日指标下载完成，共 {len(数据)} 条")
                else:
                    self.logger.debug(f"{交易日期} 每日指标无数据")
                    self.下载进度[任务键] = {
                        "状态": "完成",
                        "完成时间": datetime.now().isoformat(),
                        "数据条数": 0
                    }
                    self._保存下载进度()

            except Exception as e:
                self.logger.error(f"下载 {交易日期} 每日指标失败: {e}")
                self.下载进度[任务键] = {
                    "状态": "失败",
                    "错误时间": datetime.now().isoformat(),
                    "错误信息": str(e)
                }
                self._保存下载进度()

    def 下载每日指标(self, 开始日期=None, 结束日期=None):
        """
        下载每日指标数据（包含PE、PB、PS、股息率等财务指标）

        参数:
            开始日期 (str): 开始日期，格式YYYYMMDD
            结束日期 (str): 结束日期，格式YYYYMMDD
        """
        if 开始日期 is None:
            开始日期 = "20200101"
        if 结束日期 is None:
            结束日期 = datetime.now().strftime("%Y%m%d")

        接口名称 = "daily_basic"

        # 获取交易日历
        交易日历 = self.db.查询数据("""
            SELECT cal_date FROM 交易日历
            WHERE cal_date >= ? AND cal_date <= ? AND is_open = 1
            ORDER BY cal_date
        """, (开始日期, 结束日期))

        if 交易日历.empty:
            self.logger.warning("没有找到交易日历数据，请先下载交易日历")
            return

        交易日期列表 = 交易日历['cal_date'].tolist()
        总日期数 = len(交易日期列表)

        self.logger.info(f"开始下载每日指标，交易日期数量: {总日期数}, 日期范围: {开始日期} - {结束日期}")

        for i, 交易日期 in enumerate(交易日期列表):
            任务键 = f"{接口名称}_{交易日期}"

            if self.下载进度.get(任务键, {}).get("状态") == "完成":
                continue

            self.logger.info(f"下载 {交易日期} 每日指标 ({i+1}/{总日期数})")

            try:
                参数 = {"trade_date": 交易日期}

                # 记录开始
                self.下载进度[任务键] = {"状态": "进行中", "开始时间": datetime.now().isoformat()}
                self._保存下载进度()

                # 下载数据
                数据 = self._调用API(接口名称, **参数)

                if 数据 is not None and not 数据.empty:
                    # 保存到数据库
                    self.db.插入数据("每日指标", 数据)

                    # 记录完成
                    self.下载进度[任务键] = {
                        "状态": "完成",
                        "完成时间": datetime.now().isoformat(),
                        "数据条数": len(数据)
                    }
                    self._保存下载进度()

                    self.logger.debug(f"{交易日期} 每日指标下载完成，共 {len(数据)} 条")
                else:
                    self.logger.debug(f"{交易日期} 每日指标无数据")
                    self.下载进度[任务键] = {
                        "状态": "完成",
                        "完成时间": datetime.now().isoformat(),
                        "数据条数": 0
                    }
                    self._保存下载进度()

            except Exception as e:
                self.logger.error(f"下载 {交易日期} 每日指标失败: {e}")
                self.下载进度[任务键] = {
                    "状态": "失败",
                    "错误时间": datetime.now().isoformat(),
                    "错误信息": str(e)
                }
                self._保存下载进度()

    def 下载复权因子(self, 开始日期=None, 结束日期=None, 股票代码列表=None):
        """
        下载复权因子数据

        参数:
            开始日期 (str): 开始日期，格式YYYYMMDD
            结束日期 (str): 结束日期，格式YYYYMMDD
            股票代码列表 (list): 股票代码列表，为None时下载所有股票
        """
        if 开始日期 is None:
            开始日期 = "20200101"
        if 结束日期 is None:
            结束日期 = datetime.now().strftime("%Y%m%d")

        if 股票代码列表 is None:
            股票代码列表 = self.获取股票列表()

        接口名称 = "adj_factor"
        总股票数 = len(股票代码列表)

        self.logger.info(f"开始下载复权因子，股票数量: {总股票数}, 日期范围: {开始日期} - {结束日期}")

        for i, 股票代码 in enumerate(股票代码列表):
            任务键 = f"{接口名称}_{股票代码}_{开始日期}_{结束日期}"

            if self.下载进度.get(任务键, {}).get("状态") == "完成":
                continue

            self.logger.info(f"下载 {股票代码} 复权因子 ({i+1}/{总股票数})")

            try:
                参数 = {
                    "ts_code": 股票代码,
                    "start_date": 开始日期,
                    "end_date": 结束日期
                }

                # 记录开始
                self.下载进度[任务键] = {"状态": "进行中", "开始时间": datetime.now().isoformat()}
                self._保存下载进度()

                # 下载数据
                数据 = self._调用API(接口名称, **参数)

                if 数据 is not None and not 数据.empty:
                    # 保存到数据库
                    self.db.插入数据("复权因子", 数据)

                    # 记录完成
                    self.下载进度[任务键] = {
                        "状态": "完成",
                        "完成时间": datetime.now().isoformat(),
                        "数据条数": len(数据)
                    }
                    self._保存下载进度()

                    self.logger.debug(f"{股票代码} 复权因子下载完成，共 {len(数据)} 条")
                else:
                    self.logger.debug(f"{股票代码} 复权因子无数据")
                    self.下载进度[任务键] = {
                        "状态": "完成",
                        "完成时间": datetime.now().isoformat(),
                        "数据条数": 0
                    }
                    self._保存下载进度()

            except Exception as e:
                self.logger.error(f"下载 {股票代码} 复权因子失败: {e}")
                self.下载进度[任务键] = {
                    "状态": "失败",
                    "错误时间": datetime.now().isoformat(),
                    "错误信息": str(e)
                }
                self._保存下载进度()

    def 下载分红送股(self, 开始年份=2020, 结束年份=None):
        """
        下载分红送股数据（用于计算股息率）

        参数:
            开始年份 (int): 开始年份
            结束年份 (int): 结束年份，默认为当前年份
        """
        if 结束年份 is None:
            结束年份 = datetime.now().year

        接口名称 = "dividend"

        for 年份 in range(开始年份, 结束年份 + 1):
            任务键 = f"{接口名称}_{年份}"

            if self.下载进度.get(任务键, {}).get("状态") == "完成":
                self.logger.info(f"任务 {任务键} 已完成，跳过")
                continue

            self.logger.info(f"下载 {年份} 年分红送股数据...")

            try:
                开始日期 = f"{年份}0101"
                结束日期 = f"{年份}1231"
                参数 = {"ann_date": 开始日期, "end_date": 结束日期}

                # 记录开始
                self.下载进度[任务键] = {"状态": "进行中", "开始时间": datetime.now().isoformat()}
                self._保存下载进度()

                # 下载数据
                数据 = self._调用API(接口名称, **参数)

                if 数据 is not None and not 数据.empty:
                    # 保存到数据库
                    self.db.插入数据("分红送股", 数据)

                    # 记录完成
                    self.下载进度[任务键] = {
                        "状态": "完成",
                        "完成时间": datetime.now().isoformat(),
                        "数据条数": len(数据)
                    }
                    self._保存下载进度()

                    self.logger.info(f"{年份} 年分红送股数据下载完成，共 {len(数据)} 条")
                else:
                    self.logger.warning(f"{年份} 年分红送股数据下载结果为空")
                    self.下载进度[任务键] = {
                        "状态": "完成",
                        "完成时间": datetime.now().isoformat(),
                        "数据条数": 0
                    }
                    self._保存下载进度()

            except Exception as e:
                self.logger.error(f"下载 {年份} 年分红送股数据失败: {e}")
                self.下载进度[任务键] = {
                    "状态": "失败",
                    "错误时间": datetime.now().isoformat(),
                    "错误信息": str(e)
                }
                self._保存下载进度()

    def 下载财务数据(self, 开始年份=2020, 结束年份=None):
        """
        下载财务报表数据（利润表、资产负债表、现金流量表）

        参数:
            开始年份 (int): 开始年份
            结束年份 (int): 结束年份，默认为当前年份
        """
        if 结束年份 is None:
            结束年份 = datetime.now().year

        财务接口列表 = ["income", "balancesheet", "cashflow"]
        财务接口名称 = {"income": "利润表", "balancesheet": "资产负债表", "cashflow": "现金流量表"}

        for 接口名称 in 财务接口列表:
            表名 = 财务接口名称[接口名称]

            for 年份 in range(开始年份, 结束年份 + 1):
                for 季度 in ['0331', '0630', '0930', '1231']:
                    报告期 = f"{年份}{季度}"
                    任务键 = f"{接口名称}_{报告期}"

                    if self.下载进度.get(任务键, {}).get("状态") == "完成":
                        continue

                    self.logger.info(f"下载 {报告期} {表名}...")

                    try:
                        参数 = {"period": 报告期}

                        # 记录开始
                        self.下载进度[任务键] = {"状态": "进行中", "开始时间": datetime.now().isoformat()}
                        self._保存下载进度()

                        # 下载数据
                        数据 = self._调用API(接口名称, **参数)

                        if 数据 is not None and not 数据.empty:
                            # 保存到数据库
                            self.db.插入数据(表名, 数据)

                            # 记录完成
                            self.下载进度[任务键] = {
                                "状态": "完成",
                                "完成时间": datetime.now().isoformat(),
                                "数据条数": len(数据)
                            }
                            self._保存下载进度()

                            self.logger.debug(f"{报告期} {表名}下载完成，共 {len(数据)} 条")
                        else:
                            self.logger.debug(f"{报告期} {表名}无数据")
                            self.下载进度[任务键] = {
                                "状态": "完成",
                                "完成时间": datetime.now().isoformat(),
                                "数据条数": 0
                            }
                            self._保存下载进度()

                    except Exception as e:
                        self.logger.error(f"下载 {报告期} {表名}失败: {e}")
                        self.下载进度[任务键] = {
                            "状态": "失败",
                            "错误时间": datetime.now().isoformat(),
                            "错误信息": str(e)
                        }
                        self._保存下载进度()

    def 显示下载统计(self):
        """显示下载统计信息"""
        print("\n" + "="*60)
        print("下载统计信息")
        print("="*60)
        print(f"总API调用次数: {self.下载统计['总调用次数']}")
        print(f"成功调用次数: {self.下载统计['成功次数']}")
        print(f"失败调用次数: {self.下载统计['失败次数']}")
        print(f"下载数据条数: {self.下载统计['下载数据条数']}")
        
        if self.下载统计['开始时间']:
            开始时间 = self.下载统计['开始时间']
            结束时间 = self.下载统计['结束时间'] or datetime.now()
            耗时 = (结束时间 - 开始时间).total_seconds()
            print(f"总耗时: {耗时:.2f} 秒")
            
            if 耗时 > 0:
                print(f"平均调用速度: {self.下载统计['总调用次数']/耗时:.2f} 次/秒")
        
        print("="*60)
        
        # 显示数据库统计
        表信息 = self.db.获取所有表信息()
        print("\n数据库统计:")
        for 表名, 信息 in 表信息.items():
            if 表名 != "下载进度记录":
                print(f"  {表名}: {信息.get('记录数', 0)} 条记录")

def main():
    """主函数"""
    print("Tushare数据下载器启动...")
    
    # 创建下载器
    下载器 = Tushare数据下载器()
    下载器.下载统计["开始时间"] = datetime.now()
    
    try:
        # 下载基础数据
        下载器.下载股票基础信息()
        下载器.下载交易日历(2020, 2024)
        
        # 下载行情数据（示例：只下载前10只股票的数据）
        股票列表 = 下载器.获取股票列表()[:10]  # 限制数量避免测试时间过长
        下载器.下载日线行情("20240101", None, 股票列表)
        
    except KeyboardInterrupt:
        print("\n用户中断下载")
    except Exception as e:
        print(f"下载过程出错: {e}")
    finally:
        下载器.下载统计["结束时间"] = datetime.now()
        下载器.显示下载统计()
        下载器.db.关闭连接()

if __name__ == "__main__":
    main()
