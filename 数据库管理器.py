#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库管理器
负责创建和管理SQLite数据库，存储下载的股票数据
"""

import sqlite3
import pandas as pd
import json
import os
from datetime import datetime
import logging

class 数据库管理器:
    """SQLite数据库管理器"""
    
    def __init__(self, 数据库文件="股票数据.db"):
        """
        初始化数据库管理器
        
        参数:
            数据库文件 (str): 数据库文件路径
        """
        self.数据库文件 = 数据库文件
        self.连接 = None
        
        # 设置日志
        self.logger = logging.getLogger(__name__)
        
        # 初始化数据库
        self._初始化数据库()
        
        # 定义表结构
        self.表结构定义 = {
            "股票基础信息": """
                CREATE TABLE IF NOT EXISTS 股票基础信息 (
                    ts_code TEXT PRIMARY KEY,
                    symbol TEXT,
                    name TEXT,
                    area TEXT,
                    industry TEXT,
                    market TEXT,
                    list_date TEXT,
                    fullname TEXT,
                    enname TEXT,
                    cnspell TEXT,
                    更新时间 TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """,
            "交易日历": """
                CREATE TABLE IF NOT EXISTS 交易日历 (
                    cal_date TEXT PRIMARY KEY,
                    exchange TEXT,
                    is_open INTEGER,
                    pretrade_date TEXT,
                    更新时间 TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """,
            "日线行情": """
                CREATE TABLE IF NOT EXISTS 日线行情 (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    ts_code TEXT,
                    trade_date TEXT,
                    open REAL,
                    high REAL,
                    low REAL,
                    close REAL,
                    pre_close REAL,
                    change REAL,
                    pct_chg REAL,
                    vol REAL,
                    amount REAL,
                    更新时间 TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(ts_code, trade_date)
                )
            """,
            "每日基本指标": """
                CREATE TABLE IF NOT EXISTS 每日基本指标 (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    ts_code TEXT,
                    trade_date TEXT,
                    close REAL,
                    turnover_rate REAL,
                    turnover_rate_f REAL,
                    volume_ratio REAL,
                    pe REAL,
                    pe_ttm REAL,
                    pb REAL,
                    ps REAL,
                    ps_ttm REAL,
                    dv_ratio REAL,
                    dv_ttm REAL,
                    total_share REAL,
                    float_share REAL,
                    free_share REAL,
                    total_mv REAL,
                    circ_mv REAL,
                    更新时间 TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(ts_code, trade_date)
                )
            """,
            "涨跌停价格": """
                CREATE TABLE IF NOT EXISTS 涨跌停价格 (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    ts_code TEXT,
                    trade_date TEXT,
                    up_limit REAL,
                    down_limit REAL,
                    更新时间 TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(ts_code, trade_date)
                )
            """,
            "停复牌信息": """
                CREATE TABLE IF NOT EXISTS 停复牌信息 (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    ts_code TEXT,
                    suspend_date TEXT,
                    resume_date TEXT,
                    suspend_reason TEXT,
                    更新时间 TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(ts_code, suspend_date)
                )
            """,
            "复权因子": """
                CREATE TABLE IF NOT EXISTS 复权因子 (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    ts_code TEXT,
                    trade_date TEXT,
                    adj_factor REAL,
                    更新时间 TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(ts_code, trade_date)
                )
            """,
            "每日指标": """
                CREATE TABLE IF NOT EXISTS 每日指标 (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    ts_code TEXT,
                    trade_date TEXT,
                    close REAL,
                    turnover_rate REAL,
                    turnover_rate_f REAL,
                    volume_ratio REAL,
                    pe REAL,
                    pe_ttm REAL,
                    pb REAL,
                    ps REAL,
                    ps_ttm REAL,
                    dv_ratio REAL,
                    dv_ttm REAL,
                    total_share REAL,
                    float_share REAL,
                    free_share REAL,
                    total_mv REAL,
                    circ_mv REAL,
                    更新时间 TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(ts_code, trade_date)
                )
            """,
            "分红送股": """
                CREATE TABLE IF NOT EXISTS 分红送股 (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    ts_code TEXT,
                    end_date TEXT,
                    ann_date TEXT,
                    div_proc TEXT,
                    stk_div REAL,
                    stk_bo_rate REAL,
                    stk_co_rate REAL,
                    cash_div REAL,
                    cash_div_tax REAL,
                    record_date TEXT,
                    ex_date TEXT,
                    pay_date TEXT,
                    div_listdate TEXT,
                    imp_ann_date TEXT,
                    base_date TEXT,
                    base_share REAL,
                    更新时间 TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(ts_code, ann_date, end_date)
                )
            """,
            "上市公司信息": """
                CREATE TABLE IF NOT EXISTS 上市公司信息 (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    ts_code TEXT,
                    exchange TEXT,
                    chairman TEXT,
                    manager TEXT,
                    secretary TEXT,
                    reg_capital REAL,
                    setup_date TEXT,
                    province TEXT,
                    city TEXT,
                    introduction TEXT,
                    website TEXT,
                    email TEXT,
                    office TEXT,
                    employees INTEGER,
                    main_business TEXT,
                    business_scope TEXT,
                    更新时间 TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(ts_code)
                )
            """,
            "利润表": """
                CREATE TABLE IF NOT EXISTS 利润表 (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    ts_code TEXT,
                    ann_date TEXT,
                    f_ann_date TEXT,
                    end_date TEXT,
                    report_type TEXT,
                    comp_type TEXT,
                    basic_eps REAL,
                    diluted_eps REAL,
                    total_revenue REAL,
                    revenue REAL,
                    int_income REAL,
                    prem_earned REAL,
                    comm_income REAL,
                    n_commis_income REAL,
                    n_oth_income REAL,
                    n_oth_b_income REAL,
                    prem_income REAL,
                    out_prem REAL,
                    une_prem_reser REAL,
                    reins_income REAL,
                    n_sec_tb_income REAL,
                    n_sec_uw_income REAL,
                    n_asset_mg_income REAL,
                    oth_b_income REAL,
                    fv_value_chg_gain REAL,
                    invest_income REAL,
                    ass_invest_income REAL,
                    forex_gain REAL,
                    total_cogs REAL,
                    oper_cost REAL,
                    int_exp REAL,
                    comm_exp REAL,
                    biz_tax_surchg REAL,
                    sell_exp REAL,
                    admin_exp REAL,
                    fin_exp REAL,
                    assets_impair_loss REAL,
                    prem_refund REAL,
                    compens_payout REAL,
                    reser_insur_liab REAL,
                    div_payt REAL,
                    reins_exp REAL,
                    oper_exp REAL,
                    compens_payout_refu REAL,
                    insur_reser_refu REAL,
                    reins_cost_refund REAL,
                    other_bus_cost REAL,
                    operate_profit REAL,
                    non_oper_income REAL,
                    non_oper_exp REAL,
                    nca_disploss REAL,
                    total_profit REAL,
                    income_tax REAL,
                    n_income REAL,
                    n_income_attr_p REAL,
                    minority_gain REAL,
                    oth_compr_income REAL,
                    t_compr_income REAL,
                    compr_inc_attr_p REAL,
                    compr_inc_attr_m_s REAL,
                    ebit REAL,
                    ebitda REAL,
                    insurance_exp REAL,
                    undist_profit REAL,
                    distable_profit REAL,
                    更新时间 TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(ts_code, end_date, report_type)
                )
            """,
            "资产负债表": """
                CREATE TABLE IF NOT EXISTS 资产负债表 (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    ts_code TEXT,
                    ann_date TEXT,
                    f_ann_date TEXT,
                    end_date TEXT,
                    report_type TEXT,
                    comp_type TEXT,
                    total_share REAL,
                    cap_rese REAL,
                    undistr_porfit REAL,
                    surplus_rese REAL,
                    special_rese REAL,
                    money_cap REAL,
                    trad_asset REAL,
                    notes_receiv REAL,
                    accounts_receiv REAL,
                    oth_receiv REAL,
                    prepayment REAL,
                    div_receiv REAL,
                    int_receiv REAL,
                    inventories REAL,
                    amor_exp REAL,
                    nca_within_1y REAL,
                    sett_rsrv REAL,
                    loanto_oth_bank_fi REAL,
                    premium_receiv REAL,
                    reinsur_receiv REAL,
                    reinsur_res_receiv REAL,
                    pur_resale_fa REAL,
                    oth_cur_assets REAL,
                    total_cur_assets REAL,
                    fa_avail_for_sale REAL,
                    htm_invest REAL,
                    lt_eqt_invest REAL,
                    invest_real_estate REAL,
                    time_deposits REAL,
                    oth_assets REAL,
                    lt_rec REAL,
                    fix_assets REAL,
                    cip REAL,
                    const_materials REAL,
                    fixed_assets_disp REAL,
                    produc_bio_assets REAL,
                    oil_and_gas_assets REAL,
                    intan_assets REAL,
                    r_and_d REAL,
                    goodwill REAL,
                    lt_amor_exp REAL,
                    defer_tax_assets REAL,
                    decr_in_disbur REAL,
                    oth_nca REAL,
                    total_nca REAL,
                    cash_reser_cb REAL,
                    depos_in_oth_bfi REAL,
                    prec_metals REAL,
                    deriv_assets REAL,
                    rr_reins_une_prem REAL,
                    rr_reins_outstd_cla REAL,
                    rr_reins_lins_liab REAL,
                    rr_reins_lthins_liab REAL,
                    refund_depos REAL,
                    ph_pledge_loans REAL,
                    refund_cap_depos REAL,
                    indep_acct_assets REAL,
                    client_depos REAL,
                    client_prov REAL,
                    transac_seat_fee REAL,
                    invest_as_receiv REAL,
                    total_assets REAL,
                    lt_borr REAL,
                    st_borr REAL,
                    cb_borr REAL,
                    depos_ib_deposits REAL,
                    loan_oth_bank REAL,
                    trading_fl REAL,
                    notes_payable REAL,
                    acct_payable REAL,
                    adv_receipts REAL,
                    sold_for_repur_fa REAL,
                    comm_payable REAL,
                    payroll_payable REAL,
                    taxes_payable REAL,
                    int_payable REAL,
                    div_payable REAL,
                    oth_payable REAL,
                    acc_exp REAL,
                    deferred_inc REAL,
                    st_bonds_payable REAL,
                    payable_to_reinsurer REAL,
                    rsrv_insur_cont REAL,
                    acting_trading_sec REAL,
                    acting_uw_sec REAL,
                    non_cur_liab_due_1y REAL,
                    oth_cur_liab REAL,
                    total_cur_liab REAL,
                    bond_payable REAL,
                    lt_payable REAL,
                    specific_payables REAL,
                    estimated_liab REAL,
                    defer_tax_liab REAL,
                    defer_inc_non_cur_liab REAL,
                    oth_ncl REAL,
                    total_ncl REAL,
                    depos_oth_bfi REAL,
                    deriv_liab REAL,
                    depos REAL,
                    agency_bus_liab REAL,
                    oth_liab REAL,
                    prem_receiv_adva REAL,
                    depos_received REAL,
                    ph_invest REAL,
                    reser_une_prem REAL,
                    reser_outstd_claims REAL,
                    reser_lins_liab REAL,
                    reser_lthins_liab REAL,
                    indept_acc_liab REAL,
                    pledge_borr REAL,
                    indem_payable REAL,
                    policy_div_payable REAL,
                    total_liab REAL,
                    treasury_share REAL,
                    ordin_risk_reser REAL,
                    forex_differ REAL,
                    invest_loss_unconf REAL,
                    minority_int REAL,
                    total_hldr_eqy_exc_min_int REAL,
                    total_hldr_eqy_inc_min_int REAL,
                    total_liab_hldr_eqy REAL,
                    lt_payroll_payable REAL,
                    oth_comp_income REAL,
                    oth_eqt_tools REAL,
                    oth_eqt_tools_p_shr REAL,
                    lending_funds REAL,
                    acc_receivable REAL,
                    st_fin_payable REAL,
                    payables REAL,
                    hfs_assets REAL,
                    hfs_sales REAL,
                    更新时间 TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(ts_code, end_date, report_type)
                )
            """,
            "现金流量表": """
                CREATE TABLE IF NOT EXISTS 现金流量表 (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    ts_code TEXT,
                    ann_date TEXT,
                    f_ann_date TEXT,
                    end_date TEXT,
                    report_type TEXT,
                    comp_type TEXT,
                    net_profit REAL,
                    finan_exp REAL,
                    c_fr_sale_sg REAL,
                    recp_tax_rends REAL,
                    n_depos_incr_fi REAL,
                    n_incr_loans_cb REAL,
                    n_inc_borr_oth_fi REAL,
                    prem_fr_orig_contr REAL,
                    n_incr_insured_dep REAL,
                    n_reinsur_prem REAL,
                    n_incr_disp_tfa REAL,
                    ifc_cash_incr REAL,
                    n_incr_disp_faas REAL,
                    n_incr_loans_oth_bank REAL,
                    n_cap_incr_repur REAL,
                    c_fr_oth_operate_a REAL,
                    c_inf_fr_operate_a REAL,
                    c_paid_goods_s REAL,
                    c_paid_to_for_empl REAL,
                    c_paid_for_taxes REAL,
                    n_incr_clt_loan_adv REAL,
                    n_incr_dep_cbob REAL,
                    c_pay_claims_orig_inco REAL,
                    pay_handling_chrg REAL,
                    pay_comm_insur_plcy REAL,
                    oth_cash_pay_oper_act REAL,
                    st_cash_out_act REAL,
                    n_cashflow_act REAL,
                    oth_recp_ral_inv_act REAL,
                    c_disp_withdrwl_invest REAL,
                    c_recp_return_invest REAL,
                    n_recp_disp_fiolta REAL,
                    n_recp_disp_sobu REAL,
                    stot_inflows_inv_act REAL,
                    c_pay_acq_const_fiolta REAL,
                    c_paid_invest REAL,
                    n_disp_subs_oth_biz REAL,
                    oth_pay_ral_inv_act REAL,
                    n_incr_pledge_loan REAL,
                    stot_out_inv_act REAL,
                    n_cashflow_inv_act REAL,
                    c_recp_borrow REAL,
                    proc_issue_bonds REAL,
                    oth_cash_recp_ral_fnc_act REAL,
                    stot_cash_in_fnc_act REAL,
                    free_cashflow REAL,
                    c_prepay_amt_borr REAL,
                    c_pay_dist_dpcp_int_exp REAL,
                    incl_dvd_profit_paid_sc_ms REAL,
                    oth_cashpay_ral_fnc_act REAL,
                    stot_cashout_fnc_act REAL,
                    n_cash_flows_fnc_act REAL,
                    eff_fx_flu_cash REAL,
                    n_incr_cash_cash_equ REAL,
                    c_cash_equ_beg_period REAL,
                    c_cash_equ_end_period REAL,
                    c_recp_cap_contrib REAL,
                    incl_cash_rec_saims REAL,
                    uncon_invest_loss REAL,
                    prov_depr_assets REAL,
                    depr_fa_coga_dpba REAL,
                    amort_intang_assets REAL,
                    lt_amort_deferred_exp REAL,
                    decr_deferred_exp REAL,
                    incr_acc_exp REAL,
                    loss_disp_fiolta REAL,
                    loss_scr_fa REAL,
                    loss_fv_chg REAL,
                    invest_loss REAL,
                    decr_def_inc_tax_assets REAL,
                    incr_def_inc_tax_liab REAL,
                    decr_inventories REAL,
                    decr_oper_payable REAL,
                    incr_oper_payable REAL,
                    others REAL,
                    im_net_cashflow_oper_act REAL,
                    conv_debt_into_cap REAL,
                    conv_copbonds_due_within_1y REAL,
                    fa_fnc_leases REAL,
                    end_bal_cash REAL,
                    beg_bal_cash REAL,
                    end_bal_cash_equ REAL,
                    beg_bal_cash_equ REAL,
                    im_n_incr_cash_equ REAL,
                    更新时间 TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(ts_code, end_date, report_type)
                )
            """,
            "下载进度记录": """
                CREATE TABLE IF NOT EXISTS 下载进度记录 (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    接口名称 TEXT,
                    参数信息 TEXT,
                    开始时间 TIMESTAMP,
                    结束时间 TIMESTAMP,
                    状态 TEXT,
                    下载条数 INTEGER,
                    错误信息 TEXT,
                    更新时间 TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """
        }
        
        # 创建所有表
        self._创建所有表()
    
    def _初始化数据库(self):
        """初始化数据库连接"""
        try:
            self.连接 = sqlite3.connect(self.数据库文件, check_same_thread=False)
            self.连接.execute("PRAGMA foreign_keys = ON")  # 启用外键约束
            self.连接.execute("PRAGMA journal_mode = WAL")  # 启用WAL模式提高并发性能
            self.logger.info(f"数据库连接成功: {self.数据库文件}")
        except Exception as e:
            self.logger.error(f"数据库连接失败: {e}")
            raise
    
    def _创建所有表(self):
        """创建所有数据表"""
        try:
            cursor = self.连接.cursor()
            for 表名, 建表语句 in self.表结构定义.items():
                cursor.execute(建表语句)
                self.logger.debug(f"表 {表名} 创建/检查完成")
            
            self.连接.commit()
            self.logger.info("所有数据表创建完成")
        except Exception as e:
            self.logger.error(f"创建数据表失败: {e}")
            raise
    
    def 插入数据(self, 表名, 数据, 如果存在则替换=False):
        """
        插入数据到指定表

        参数:
            表名 (str): 目标表名
            数据 (pd.DataFrame): 要插入的数据
            如果存在则替换 (bool): 是否替换已存在的数据（默认为False，使用追加模式）

        返回:
            int: 插入的行数
        """
        if 数据.empty:
            self.logger.warning(f"尝试插入空数据到表 {表名}")
            return 0

        try:
            # 添加更新时间列
            if '更新时间' not in 数据.columns:
                数据 = 数据.copy()
                数据['更新时间'] = datetime.now()

            # 对于日线行情数据，使用特殊的插入逻辑避免重复
            if 表名 == '日线行情':
                # 先删除已存在的相同股票和日期的数据，然后插入新数据
                for _, row in 数据.iterrows():
                    # 删除已存在的记录
                    删除SQL = "DELETE FROM 日线行情 WHERE ts_code = ? AND trade_date = ?"
                    self.连接.execute(删除SQL, (row['ts_code'], row['trade_date']))

                # 使用append模式插入新数据
                数据.to_sql(表名, self.连接, if_exists='append', index=False)
            else:
                # 其他表使用原来的逻辑
                if_exists = 'replace' if 如果存在则替换 else 'append'
                数据.to_sql(表名, self.连接, if_exists=if_exists, index=False)

            self.logger.info(f"成功插入 {len(数据)} 条数据到表 {表名}")
            return len(数据)

        except Exception as e:
            self.logger.error(f"插入数据到表 {表名} 失败: {e}")
            raise
    
    def 查询数据(self, sql语句, 参数=None):
        """
        执行查询语句
        
        参数:
            sql语句 (str): SQL查询语句
            参数 (tuple): 查询参数
            
        返回:
            pd.DataFrame: 查询结果
        """
        try:
            if 参数:
                结果 = pd.read_sql_query(sql语句, self.连接, params=参数)
            else:
                结果 = pd.read_sql_query(sql语句, self.连接)
            
            self.logger.debug(f"查询成功，返回 {len(结果)} 条记录")
            return 结果
            
        except Exception as e:
            self.logger.error(f"查询失败: {e}")
            raise
    
    def 获取表信息(self, 表名):
        """
        获取表的基本信息
        
        参数:
            表名 (str): 表名
            
        返回:
            dict: 表信息
        """
        try:
            # 获取记录数
            记录数查询 = f"SELECT COUNT(*) as count FROM {表名}"
            记录数 = self.查询数据(记录数查询).iloc[0]['count']
            
            # 获取表结构
            结构查询 = f"PRAGMA table_info({表名})"
            表结构 = self.查询数据(结构查询)
            
            # 获取最新更新时间
            try:
                时间查询 = f"SELECT MAX(更新时间) as latest FROM {表名}"
                最新时间 = self.查询数据(时间查询).iloc[0]['latest']
            except:
                最新时间 = None
            
            return {
                "表名": 表名,
                "记录数": 记录数,
                "字段数": len(表结构),
                "字段列表": 表结构['name'].tolist(),
                "最新更新时间": 最新时间
            }
            
        except Exception as e:
            self.logger.error(f"获取表 {表名} 信息失败: {e}")
            return {"表名": 表名, "错误": str(e)}
    
    def 获取所有表信息(self):
        """
        获取所有表的信息
        
        返回:
            dict: 所有表的信息
        """
        所有表信息 = {}
        
        # 获取所有表名
        表名查询 = "SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'"
        表名列表 = self.查询数据(表名查询)['name'].tolist()
        
        for 表名 in 表名列表:
            所有表信息[表名] = self.获取表信息(表名)
        
        return 所有表信息
    
    def 记录下载进度(self, 接口名称, 参数信息, 状态, 下载条数=0, 错误信息=None):
        """
        记录下载进度
        
        参数:
            接口名称 (str): API接口名称
            参数信息 (dict): 调用参数
            状态 (str): 下载状态 (开始/完成/失败)
            下载条数 (int): 下载的数据条数
            错误信息 (str): 错误信息
        """
        try:
            进度数据 = pd.DataFrame([{
                '接口名称': 接口名称,
                '参数信息': json.dumps(参数信息, ensure_ascii=False),
                '开始时间': datetime.now() if 状态 == '开始' else None,
                '结束时间': datetime.now() if 状态 in ['完成', '失败'] else None,
                '状态': 状态,
                '下载条数': 下载条数,
                '错误信息': 错误信息
            }])
            
            self.插入数据('下载进度记录', 进度数据, 如果存在则替换=False)
            
        except Exception as e:
            self.logger.error(f"记录下载进度失败: {e}")
    
    def 检查数据是否存在(self, 表名, 条件字典):
        """
        检查数据是否已存在
        
        参数:
            表名 (str): 表名
            条件字典 (dict): 查询条件
            
        返回:
            bool: 数据是否存在
        """
        try:
            条件列表 = []
            参数列表 = []
            
            for 字段, 值 in 条件字典.items():
                条件列表.append(f"{字段} = ?")
                参数列表.append(值)
            
            where条件 = " AND ".join(条件列表)
            sql = f"SELECT COUNT(*) as count FROM {表名} WHERE {where条件}"
            
            结果 = self.查询数据(sql, tuple(参数列表))
            return 结果.iloc[0]['count'] > 0
            
        except Exception as e:
            self.logger.error(f"检查数据存在性失败: {e}")
            return False
    
    def 备份数据库(self, 备份路径=None):
        """
        备份数据库
        
        参数:
            备份路径 (str): 备份文件路径
            
        返回:
            str: 备份文件路径
        """
        if not 备份路径:
            时间戳 = datetime.now().strftime('%Y%m%d_%H%M%S')
            备份路径 = f"股票数据_备份_{时间戳}.db"
        
        try:
            # 创建备份
            备份连接 = sqlite3.connect(备份路径)
            self.连接.backup(备份连接)
            备份连接.close()
            
            self.logger.info(f"数据库备份成功: {备份路径}")
            return 备份路径
            
        except Exception as e:
            self.logger.error(f"数据库备份失败: {e}")
            raise
    
    def 关闭连接(self):
        """关闭数据库连接"""
        if self.连接:
            self.连接.close()
            self.logger.info("数据库连接已关闭")

def 测试数据库管理器():
    """测试数据库管理器功能"""
    print("测试数据库管理器...")
    
    # 创建测试数据库
    db = 数据库管理器("测试数据库.db")
    
    # 测试插入数据
    测试数据 = pd.DataFrame([
        {'ts_code': '000001.SZ', 'symbol': '000001', 'name': '平安银行'},
        {'ts_code': '000002.SZ', 'symbol': '000002', 'name': '万科A'}
    ])
    
    db.插入数据('股票基础信息', 测试数据)
    
    # 测试查询数据
    查询结果 = db.查询数据("SELECT * FROM 股票基础信息")
    print("查询结果:")
    print(查询结果)
    
    # 测试获取表信息
    表信息 = db.获取所有表信息()
    print("\n表信息:")
    for 表名, 信息 in 表信息.items():
        print(f"{表名}: {信息}")
    
    # 清理测试文件
    db.关闭连接()
    os.remove("测试数据库.db")
    print("\n测试完成")

if __name__ == "__main__":
    测试数据库管理器()
