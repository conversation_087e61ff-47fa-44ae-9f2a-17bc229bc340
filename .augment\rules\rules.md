---
type: "manual"
---

# Tushare数据下载器开发规则文档

## 项目概述
本项目是一个基于Tushare API的股票数据下载器，旨在为股票分析提供便捷的数据获取工具。

## 文件命名规范

### 1. 文件名必须使用中文
- ✅ 正确示例：`股票数据下载器.py`、`配置文件.json`、`数据处理工具.py`
- ❌ 错误示例：`stock_downloader.py`、`config.json`、`data_processor.py`

### 2. 文件名格式规范
- 使用有意义的中文描述
- 避免使用特殊字符（除了下划线和连字符）
- 文件扩展名保持英文（.py, .json, .txt等）
- 示例：`主程序入口.py`、`数据库连接配置.json`、`日志记录工具.py`

## 目录结构规范

```
tushare数据下载器/
├── 主程序入口.py              # 程序主入口
├── 配置文件.json             # 配置信息
├── 开发规则文档.md           # 本文档
├── 数据下载模块/
│   ├── 股票基础数据下载器.py
│   ├── 财务数据下载器.py
│   └── 行情数据下载器.py
├── 数据处理模块/
│   ├── 数据清洗工具.py
│   ├── 数据格式转换器.py
│   └── 数据验证器.py
├── 数据存储模块/
│   ├── 数据库操作类.py
│   ├── 文件存储工具.py
│   └── 缓存管理器.py
├── 工具模块/
│   ├── 日志记录工具.py
│   ├── 异常处理器.py
│   └── 时间工具.py
├── 测试文件/
│   ├── 单元测试_数据下载.py
│   ├── 单元测试_数据处理.py
│   └── 集成测试_完整流程.py
└── 文档/
    ├── 使用说明.md
    ├── API接口文档.md
    └── 常见问题解答.md
```

## 代码编写规范

### 1. 变量命名
- 使用有意义的中文拼音或英文名称
- 类名使用大驼峰命名法：`StockDownloader`、`DataProcessor`
- 函数名使用小驼峰命名法：`downloadStockData`、`processData`
- 变量名使用下划线命名法：`stock_code`、`data_list`

### 2. 注释规范
- 所有注释必须使用中文
- 类和函数必须有详细的中文文档字符串
- 复杂逻辑必须添加行内注释

```python
class 股票数据下载器:
    """
    股票数据下载器类
    
    功能：
    - 从Tushare API获取股票数据
    - 支持多种数据类型下载
    - 提供数据缓存功能
    """
    
    def 下载股票基础信息(self, 股票代码):
        """
        下载指定股票的基础信息
        
        参数:
            股票代码 (str): 股票代码，如'000001.SZ'
            
        返回:
            dict: 包含股票基础信息的字典
        """
        # 验证股票代码格式
        if not self._验证股票代码(股票代码):
            raise ValueError("股票代码格式不正确")
        
        # 调用Tushare API获取数据
        数据 = self.tushare_client.stock_basic(ts_code=股票代码)
        return 数据
```

### 3. 错误处理规范
- 使用中文错误信息
- 建立统一的异常处理机制
- 记录详细的错误日志

## 开发流程规范

### 1. 开发前准备
1. 阅读本开发规则文档
2. 配置开发环境
3. 获取Tushare API token
4. 创建对应的中文文件名

### 2. 代码提交规范
- 提交信息使用中文描述
- 格式：`[模块名] 功能描述`
- 示例：`[数据下载模块] 新增股票基础数据下载功能`

### 3. 测试规范
- 每个功能模块必须编写对应的测试文件
- 测试文件名格式：`单元测试_模块名.py`
- 测试函数名使用中文：`测试_下载股票数据`

## 依赖管理

### 必需依赖包
```
tushare>=1.2.89
pandas>=1.3.0
numpy>=1.21.0
requests>=2.25.0
sqlalchemy>=1.4.0
pymysql>=1.0.0
```

### 安装命令
```bash
pip install tushare pandas numpy requests sqlalchemy pymysql
```

## 配置文件规范

配置文件使用JSON格式，文件名：`配置文件.json`

```json
{
    "tushare配置": {
        "api_token": "你的tushare_token",
        "请求频率限制": 200,
        "超时时间": 30
    },
    "数据库配置": {
        "主机": "localhost",
        "端口": 3306,
        "用户名": "root",
        "密码": "password",
        "数据库名": "stock_data"
    },
    "日志配置": {
        "日志级别": "INFO",
        "日志文件路径": "./logs/应用日志.log",
        "最大文件大小": "10MB",
        "备份文件数量": 5
    }
}
```

## 注意事项

1. **API限制**：注意Tushare API的调用频率限制
2. **数据存储**：合理设计数据存储结构，避免重复下载
3. **异常处理**：网络异常、API异常等都要妥善处理
4. **日志记录**：详细记录程序运行状态和错误信息
5. **代码复用**：提取公共功能，避免代码重复

## 版本控制

- 使用Git进行版本控制
- 分支命名使用中文：`功能_股票数据下载`、`修复_API调用异常`
- 定期备份重要代码和数据

---

**文档版本**: 1.0  
**创建日期**: 2025-01-31  
**维护人员**: 开发团队  
**更新记录**: 初始版本创建
