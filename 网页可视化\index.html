<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tushare股票数据可视化系统</title>
    <link rel="stylesheet" href="styles.css">
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios@1.5.0/dist/axios.min.js"></script>
</head>
<body>
    <!-- 顶部导航栏 -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <h1>📊 Tushare股票数据可视化</h1>
            </div>
            <div class="nav-menu">
                <button class="nav-btn active" data-tab="overview">市场概览</button>
                <button class="nav-btn" data-tab="stock">个股分析</button>
                <button class="nav-btn" data-tab="industry">行业分析</button>
                <button class="nav-btn" data-tab="database">数据状态</button>
                <button class="nav-btn" data-tab="detail">数据详情</button>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <main class="main-content">
        <!-- 市场概览页面 -->
        <div id="overview-tab" class="tab-content active">
            <div class="page-header">
                <h2>📈 市场概览</h2>
                <div class="refresh-btn" onclick="刷新市场概览()">🔄 刷新数据</div>
            </div>
            
            <!-- 市场统计卡片 -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-title">总股票数</div>
                    <div class="stat-value" id="total-stocks">-</div>
                </div>
                <div class="stat-card up">
                    <div class="stat-title">上涨股票</div>
                    <div class="stat-value" id="up-stocks">-</div>
                </div>
                <div class="stat-card down">
                    <div class="stat-title">下跌股票</div>
                    <div class="stat-value" id="down-stocks">-</div>
                </div>
                <div class="stat-card">
                    <div class="stat-title">平均涨跌幅</div>
                    <div class="stat-value" id="avg-change">-</div>
                </div>
                <div class="stat-card">
                    <div class="stat-title">总成交额</div>
                    <div class="stat-value" id="total-amount">-</div>
                </div>
            </div>

            <!-- 图表区域 -->
            <div class="charts-grid">
                <div class="chart-container">
                    <h3>涨跌分布</h3>
                    <div id="distribution-chart" class="chart"></div>
                </div>
                <div class="chart-container">
                    <h3>成交额前20股票</h3>
                    <div id="top-amount-chart" class="chart"></div>
                </div>
                <div class="chart-container">
                    <h3>行业平均涨跌幅</h3>
                    <div id="industry-change-chart" class="chart"></div>
                </div>
                <div class="chart-container">
                    <h3>行业成交额分布</h3>
                    <div id="industry-amount-chart" class="chart"></div>
                </div>
            </div>
        </div>

        <!-- 个股分析页面 -->
        <div id="stock-tab" class="tab-content">
            <div class="page-header">
                <h2>📊 个股分析</h2>
                <div class="stock-controls">
                    <select id="stock-selector" onchange="切换股票()">
                        <option value="">选择股票...</option>
                    </select>
                    <input type="date" id="start-date" onchange="更新股票图表()">
                    <input type="date" id="end-date" onchange="更新股票图表()">
                    <button onclick="更新股票图表()">更新图表</button>
                </div>
            </div>

            <!-- 股票信息卡片 -->
            <div id="stock-info" class="stock-info-card" style="display: none;">
                <div class="stock-basic">
                    <h3 id="stock-name">-</h3>
                    <div class="stock-code" id="stock-code">-</div>
                </div>
                <div class="stock-stats">
                    <div class="stock-stat">
                        <span class="label">最新价:</span>
                        <span class="value" id="latest-price">-</span>
                    </div>
                    <div class="stock-stat">
                        <span class="label">涨跌幅:</span>
                        <span class="value" id="price-change">-</span>
                    </div>
                    <div class="stock-stat">
                        <span class="label">成交量:</span>
                        <span class="value" id="volume">-</span>
                    </div>
                </div>
            </div>

            <!-- K线图 -->
            <div class="chart-container full-width">
                <div id="kline-chart" class="chart large"></div>
            </div>

            <!-- 成交量图 -->
            <div class="chart-container full-width">
                <div id="volume-chart" class="chart medium"></div>
            </div>
        </div>

        <!-- 行业分析页面 -->
        <div id="industry-tab" class="tab-content">
            <div class="page-header">
                <h2>🏭 行业分析</h2>
                <div class="refresh-btn" onclick="刷新行业分析()">🔄 刷新数据</div>
            </div>

            <div class="charts-grid">
                <div class="chart-container">
                    <h3>行业涨跌幅排行</h3>
                    <div id="industry-ranking-chart" class="chart"></div>
                </div>
                <div class="chart-container">
                    <h3>行业成交额对比</h3>
                    <div id="industry-volume-chart" class="chart"></div>
                </div>
            </div>

            <!-- 行业详细表格 -->
            <div class="table-container">
                <h3>行业详细数据</h3>
                <table id="industry-table" class="data-table">
                    <thead>
                        <tr>
                            <th>行业名称</th>
                            <th>股票数量</th>
                            <th>平均涨跌幅</th>
                            <th>总成交额</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- 数据将通过JavaScript填充 -->
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 数据状态页面 -->
        <div id="database-tab" class="tab-content">
            <div class="page-header">
                <h2>💾 数据库状态</h2>
                <div class="refresh-btn" onclick="刷新数据库状态()">🔄 刷新状态</div>
            </div>

            <div class="table-container">
                <table id="database-table" class="data-table">
                    <thead>
                        <tr>
                            <th>表名</th>
                            <th>记录数量</th>
                            <th>最后更新</th>
                            <th>字段数量</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- 数据将通过JavaScript填充 -->
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 数据详情页面 -->
        <div id="detail-tab" class="tab-content">
            <div class="page-header">
                <h2>📋 股票数据详情</h2>
                <div class="refresh-btn" onclick="刷新所有股票数据()">🔄 刷新数据</div>
            </div>

            <!-- 控制区域 -->
            <div class="controls-container">
                <!-- 日期选择 -->
                <div class="date-controls">
                    <label for="trade-date-select">交易日期：</label>
                    <select id="trade-date-select" onchange="切换交易日期()">
                        <option value="">加载中...</option>
                    </select>
                </div>

                <!-- 搜索框 -->
                <div class="search-group">
                    <input type="text" id="stock-search" class="search-input" placeholder="搜索股票代码或名称...">
                    <button class="search-btn" onclick="搜索股票()">🔍 搜索</button>
                </div>

                <!-- 筛选器 -->
                <div class="filter-group">
                    <select id="industry-filter" class="filter-select">
                        <option value="">所有行业</option>
                    </select>

                    <select id="market-filter" class="filter-select">
                        <option value="">所有市场</option>
                        <option value="主板">主板</option>
                        <option value="创业板">创业板</option>
                        <option value="科创板">科创板</option>
                        <option value="北交所">北交所</option>
                    </select>

                    <button class="filter-btn" onclick="应用筛选()">筛选</button>
                    <button class="clear-btn" onclick="清除筛选()">清除</button>
                </div>
            </div>

            <!-- 数据统计信息 -->
            <div class="stats-container">
                <div class="stat-item">
                    <span class="stat-label">总股票数：</span>
                    <span class="stat-value" id="total-stocks">-</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">有数据股票：</span>
                    <span class="stat-value" id="active-stocks">-</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">最新交易日：</span>
                    <span class="stat-value" id="latest-date">-</span>
                </div>
            </div>

            <!-- 股票数据表格 -->
            <div class="table-container">
                <div class="scroll-hint" id="scroll-hint">
                    <span>💡 提示：表格可以左右滑动查看更多列</span>
                </div>
                <div class="table-wrapper">
                    <table id="stocks-data-table" class="simple-table">
                    <thead>
                        <tr>
                            <th class="sortable" data-column="ts_code">股票代码</th>
                            <th class="sortable" data-column="symbol">简称</th>
                            <th class="sortable" data-column="name">股票名称</th>
                            <th class="sortable" data-column="area">地区</th>
                            <th class="sortable" data-column="industry">行业</th>
                            <th class="sortable" data-column="market">市场</th>
                            <th class="sortable" data-column="list_date">上市日期</th>
                            <th class="sortable" data-column="act_name">实际控制人</th>
                            <th class="sortable" data-column="act_ent_type">企业类型</th>
                            <th class="sortable" data-column="trade_date">交易日期</th>
                            <th class="sortable" data-column="open">开盘价</th>
                            <th class="sortable" data-column="high">最高价</th>
                            <th class="sortable" data-column="low">最低价</th>
                            <th class="sortable" data-column="close">收盘价</th>
                            <th class="sortable" data-column="pre_close">前收盘价</th>
                            <th class="sortable" data-column="change">涨跌额</th>
                            <th class="sortable" data-column="pct_chg">涨跌幅(%)</th>
                            <th class="sortable" data-column="vol">成交量(手)</th>
                            <th class="sortable" data-column="amount">成交额(万元)</th>
                            <th class="sortable" data-column="pe">市盈率(PE)</th>
                            <th class="sortable" data-column="pe_ttm">市盈率TTM</th>
                            <th class="sortable" data-column="pb">市净率(PB)</th>
                            <th class="sortable" data-column="ps">市销率(PS)</th>
                            <th class="sortable" data-column="ps_ttm">市销率TTM</th>
                            <th class="sortable" data-column="dv_ratio">股息率(%)</th>
                            <th class="sortable" data-column="dv_ttm">股息率TTM(%)</th>
                            <th class="sortable" data-column="total_mv">总市值(万元)</th>
                            <th class="sortable" data-column="circ_mv">流通市值(万元)</th>
                            <th class="sortable" data-column="turnover_rate">换手率(%)</th>
                            <th class="sortable" data-column="volume_ratio">量比</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- 数据将通过JavaScript填充 -->
                    </tbody>
                    </table>
                </div>
            </div>

            <!-- 分页控件 -->
            <div class="pagination-container">
                <div class="pagination-info">
                    <span>显示第 <span id="page-start">1</span> - <span id="page-end">50</span> 条，共 <span id="total-records">0</span> 条记录</span>
                </div>
                <div class="pagination-controls">
                    <button class="page-btn" onclick="上一页()" id="prev-page">上一页</button>
                    <span class="page-numbers" id="page-numbers"></span>
                    <button class="page-btn" onclick="下一页()" id="next-page">下一页</button>
                </div>
                <div class="page-size-selector">
                    <label>每页显示：</label>
                    <select id="page-size" onchange="更改页面大小()">
                        <option value="50">50条</option>
                        <option value="100">100条</option>
                        <option value="200">200条</option>
                        <option value="500">500条</option>
                    </select>
                </div>
            </div>

            <!-- 加载状态 -->
            <div class="loading-container" id="stocks-loading" style="display: none;">
                <div class="loading-spinner"></div>
                <div class="loading-text">正在加载股票数据...</div>
            </div>

            <!-- 无数据提示 -->
            <div class="no-data-message" id="stocks-no-data" style="display: none;">
                <div class="no-data-icon">📭</div>
                <div class="no-data-text">暂无股票数据</div>
                <div class="no-data-hint">请检查数据是否已下载或刷新页面重试</div>
            </div>
        </div>
    </main>

    <!-- 加载提示 -->
    <div id="loading" class="loading">
        <div class="loading-spinner"></div>
        <div class="loading-text">正在加载数据...</div>
    </div>

    <!-- 错误提示 -->
    <div id="error-message" class="error-message" style="display: none;">
        <div class="error-content">
            <span class="error-text"></span>
            <button class="error-close" onclick="隐藏错误提示()">×</button>
        </div>
    </div>

    <script src="app.js"></script>
</body>
</html>
