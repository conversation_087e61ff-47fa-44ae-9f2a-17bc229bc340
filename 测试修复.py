#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
from 数据库管理器 import 数据库管理器

def 测试API修复():
    """测试API修复效果"""
    base_url = "http://127.0.0.1:5000"
    
    print("=== 测试可用交易日期API ===")
    try:
        response = requests.get(f"{base_url}/api/available-dates")
        if response.status_code == 200:
            data = response.json()
            if data['success']:
                print(f"✅ 成功获取 {len(data['dates'])} 个可用交易日期")
                print("前5个日期:")
                for i, date in enumerate(data['dates'][:5]):
                    print(f"  {i+1}. {date}")
            else:
                print(f"❌ API返回错误: {data['error']}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
    except Exception as e:
        print(f"❌ 请求失败: {e}")
    
    print("\n=== 测试特定日期的股票数据API ===")
    # 测试最新日期
    try:
        response = requests.get(f"{base_url}/api/all-stocks-data")
        if response.status_code == 200:
            data = response.json()
            if data['success']:
                print(f"✅ 最新日期 {data['query_date']} 数据:")
                print(f"  总股票数: {data['total']}")
                print(f"  有交易数据股票数: {data['data_count']}")
                
                # 检查前几条数据
                有交易数据的股票 = [股票 for 股票 in data['data'][:10] if 股票.get('trade_date') and 股票['trade_date'] != '-']
                print(f"  前10条中有交易数据的: {len(有交易数据的股票)} 条")
            else:
                print(f"❌ API返回错误: {data['error']}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
    except Exception as e:
        print(f"❌ 请求失败: {e}")
    
    print("\n=== 测试指定日期的股票数据API ===")
    # 测试指定日期（如果有的话）
    try:
        # 先获取可用日期
        response = requests.get(f"{base_url}/api/available-dates")
        if response.status_code == 200:
            dates_data = response.json()
            if dates_data['success'] and len(dates_data['dates']) > 1:
                test_date = dates_data['dates'][1]  # 使用第二个日期进行测试
                print(f"测试日期: {test_date}")
                
                response = requests.get(f"{base_url}/api/all-stocks-data?trade_date={test_date}")
                if response.status_code == 200:
                    data = response.json()
                    if data['success']:
                        print(f"✅ 指定日期 {data['query_date']} 数据:")
                        print(f"  总股票数: {data['total']}")
                        print(f"  有交易数据股票数: {data['data_count']}")
                    else:
                        print(f"❌ API返回错误: {data['error']}")
                else:
                    print(f"❌ HTTP错误: {response.status_code}")
            else:
                print("⚠️ 没有足够的可用日期进行测试")
    except Exception as e:
        print(f"❌ 请求失败: {e}")

def 检查数据库状态():
    """检查数据库状态"""
    print("\n=== 数据库状态检查 ===")
    db = 数据库管理器()
    
    # 检查最新的几个交易日期
    sql = """
        SELECT trade_date, COUNT(*) as count 
        FROM 日线行情 
        WHERE close IS NOT NULL 
        GROUP BY trade_date 
        ORDER BY trade_date DESC 
        LIMIT 10
    """
    
    result = db.查询数据(sql)
    print("最新10个有数据的交易日:")
    for _, row in result.iterrows():
        print(f"  {row['trade_date']}: {row['count']} 条数据")

if __name__ == "__main__":
    print("🔧 开始测试修复效果...")
    检查数据库状态()
    print("\n" + "="*50)
    print("⚠️ 请确保Web服务器正在运行 (python 网页数据接口.py)")
    print("然后按回车键继续测试API...")
    input()
    测试API修复()
    print("\n✅ 测试完成!")
