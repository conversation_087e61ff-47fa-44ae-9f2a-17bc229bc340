# 🎯 Tushare全面数据下载系统

## ✨ 最新功能

**🎉 现已支持完整的财务指标数据！**

- 💰 **PE/PB/PS** - 市盈率、市净率、市销率
- 📊 **股息率** - 分红收益率数据
- 💎 **市值数据** - 总市值、流通市值
- 🔄 **换手率** - 成交活跃度指标
- 📈 **量比** - 成交量比较指标

## 🚀 快速开始

### ⚡ 推荐启动方式

```bash
python 快速启动.py
```

### 🌐 完整功能启动

```bash
python 启动系统.py
```

### 🌐 Web界面访问

启动后自动打开浏览器访问：http://localhost:5000

## 📊 主要功能

### 1. 📈 市场概览
- 实时市场统计
- 涨跌分布图表
- 行业表现排行

### 2. 📋 数据详情
- 所有股票数据表格
- 搜索、筛选、排序功能
- 实时价格和涨跌幅

### 3. 🏭 行业分析
- 行业涨跌幅对比
- 板块轮动分析

### 4. 📊 数据状态
- 下载进度监控
- 数据库状态查看

## ⚙️ 配置要求

### 必需配置
在 `配置文件.json` 中设置你的Tushare API Token：

```json
{
    "tushare": {
        "token": "你的API_TOKEN"
    }
}
```

### 获取API Token
1. 注册Tushare账户：https://tushare.pro/register
2. 获取API Token
3. 填入配置文件

## 📁 核心文件

```
tushare数据下载/
├── 启动系统.py           # 🚀 主启动入口（运行这个）
├── 数据下载器.py         # 📥 数据下载模块
├── 数据库管理器.py       # 💾 数据库管理
├── 网页数据接口.py       # 🌐 Web API服务
├── 配置文件.json         # ⚙️ 系统配置
├── 股票数据.db           # 📊 数据库文件
└── 网页可视化/           # 🎨 Web前端
    ├── index.html
    ├── styles.css
    └── app.js
```

## 🔧 常见问题

### ❌ API Token错误
- 检查配置文件中的token是否正确
- 确认Tushare账户状态正常

### ❌ 端口被占用
- 修改配置文件中的端口号
- 或关闭占用5000端口的程序

### ❌ 数据下载慢
- 检查网络连接
- 等待API频率限制恢复

## 💡 使用建议

1. **首次使用**：先运行"启动Web可视化界面"查看现有数据
2. **数据下载**：在后台运行"开始下载股票数据"
3. **实时查看**：下载过程中可以刷新Web界面查看新数据
4. **断点续传**：下载中断后重新启动会自动继续

## 📞 获取帮助

运行 `python 启动系统.py` 后选择相应选项：
- 选项3：查看数据库状态
- 选项4：查看系统配置
- 选项5：查看详细说明
