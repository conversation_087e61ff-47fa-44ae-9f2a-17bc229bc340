#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Tushare API频率控制器
确保API调用不超过每分钟1000次的限制
"""

import time
import threading
from collections import deque
from datetime import datetime, timedelta
import logging

class API频率控制器:
    """API调用频率控制器"""
    
    def __init__(self, 每分钟最大调用次数=1000):
        """
        初始化频率控制器
        
        参数:
            每分钟最大调用次数 (int): 每分钟允许的最大API调用次数
        """
        self.每分钟最大调用次数 = 每分钟最大调用次数
        self.调用记录 = deque()  # 存储调用时间戳
        self.锁 = threading.Lock()  # 线程锁
        
        # 计算最小调用间隔（秒）
        self.最小调用间隔 = 60.0 / 每分钟最大调用次数
        self.上次调用时间 = 0
        
        # 设置日志
        self.logger = logging.getLogger(__name__)
        
        self.logger.info(f"频率控制器初始化完成，每分钟最大调用次数: {每分钟最大调用次数}")
        self.logger.info(f"最小调用间隔: {self.最小调用间隔:.3f} 秒")
    
    def _清理过期记录(self):
        """清理一分钟前的调用记录"""
        当前时间 = time.time()
        一分钟前 = 当前时间 - 60
        
        # 移除一分钟前的记录
        while self.调用记录 and self.调用记录[0] < 一分钟前:
            self.调用记录.popleft()
    
    def 等待调用许可(self):
        """
        等待获取API调用许可
        如果当前调用频率过高，会自动等待
        
        返回:
            float: 实际等待的时间（秒）
        """
        with self.锁:
            开始等待时间 = time.time()
            
            # 清理过期记录
            self._清理过期记录()
            
            # 检查是否需要等待（基于调用间隔）
            当前时间 = time.time()
            距离上次调用时间 = 当前时间 - self.上次调用时间
            
            if 距离上次调用时间 < self.最小调用间隔:
                需要等待时间 = self.最小调用间隔 - 距离上次调用时间
                self.logger.debug(f"基于调用间隔等待 {需要等待时间:.3f} 秒")
                time.sleep(需要等待时间)
                当前时间 = time.time()
            
            # 检查是否需要等待（基于一分钟内调用次数）
            while len(self.调用记录) >= self.每分钟最大调用次数:
                # 如果一分钟内调用次数已达上限，等待最早的记录过期
                最早记录时间 = self.调用记录[0]
                需要等待时间 = 最早记录时间 + 60 - 当前时间 + 0.1  # 多等待0.1秒确保安全
                
                if 需要等待时间 > 0:
                    self.logger.warning(f"达到频率限制，等待 {需要等待时间:.3f} 秒")
                    time.sleep(需要等待时间)
                    当前时间 = time.time()
                
                # 重新清理过期记录
                self._清理过期记录()
            
            # 记录本次调用
            self.调用记录.append(当前时间)
            self.上次调用时间 = 当前时间
            
            总等待时间 = 当前时间 - 开始等待时间
            
            if 总等待时间 > 0.01:  # 只记录明显的等待
                self.logger.debug(f"总等待时间: {总等待时间:.3f} 秒")
            
            return 总等待时间
    
    def 获取当前状态(self):
        """
        获取当前频率控制器状态
        
        返回:
            dict: 包含当前状态信息的字典
        """
        with self.锁:
            self._清理过期记录()
            
            当前时间 = time.time()
            距离上次调用 = 当前时间 - self.上次调用时间
            
            return {
                "一分钟内调用次数": len(self.调用记录),
                "剩余调用次数": self.每分钟最大调用次数 - len(self.调用记录),
                "距离上次调用秒数": 距离上次调用,
                "建议等待时间": max(0, self.最小调用间隔 - 距离上次调用),
                "当前时间": datetime.fromtimestamp(当前时间).strftime('%H:%M:%S')
            }
    
    def 重置计数器(self):
        """重置调用计数器（谨慎使用）"""
        with self.锁:
            self.调用记录.clear()
            self.上次调用时间 = 0
            self.logger.warning("频率控制器计数器已重置")

class 装饰器频率控制:
    """频率控制装饰器类"""
    
    def __init__(self, 控制器):
        """
        初始化装饰器
        
        参数:
            控制器 (API频率控制器): 频率控制器实例
        """
        self.控制器 = 控制器
    
    def __call__(self, func):
        """
        装饰器函数
        
        参数:
            func: 要装饰的函数
            
        返回:
            function: 装饰后的函数
        """
        def wrapper(*args, **kwargs):
            # 等待调用许可
            等待时间 = self.控制器.等待调用许可()
            
            try:
                # 执行原函数
                结果 = func(*args, **kwargs)
                return 结果
            except Exception as e:
                # 记录错误但不影响频率控制
                logging.getLogger(__name__).error(f"API调用出错: {e}")
                raise
        
        return wrapper

# 创建全局频率控制器实例
全局频率控制器 = API频率控制器(每分钟最大调用次数=1000)

def 频率控制(func):
    """
    频率控制装饰器
    使用全局频率控制器
    
    用法:
        @频率控制
        def my_api_call():
            # API调用代码
            pass
    """
    return 装饰器频率控制(全局频率控制器)(func)

def 测试频率控制器():
    """测试频率控制器功能"""
    print("测试频率控制器...")
    
    控制器 = API频率控制器(每分钟最大调用次数=10)  # 测试用小数值
    
    @装饰器频率控制(控制器)
    def 模拟API调用(调用编号):
        print(f"执行API调用 #{调用编号}")
        return f"结果 #{调用编号}"
    
    # 快速连续调用测试
    开始时间 = time.time()
    for i in range(15):
        状态 = 控制器.获取当前状态()
        print(f"调用前状态: {状态}")
        
        结果 = 模拟API调用(i + 1)
        print(f"调用结果: {结果}")
        print("-" * 40)
    
    结束时间 = time.time()
    print(f"总耗时: {结束时间 - 开始时间:.2f} 秒")

if __name__ == "__main__":
    测试频率控制器()
