#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
网页数据接口
为Web可视化提供数据API服务
"""

import json
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
from flask import Flask, jsonify, request, send_from_directory
from flask_cors import CORS
import os
from 数据库管理器 import 数据库管理器

class 网页数据接口:
    """网页数据接口类"""

    @staticmethod
    def 转换为JSON兼容类型(值):
        """将任何数据类型转换为JSON兼容的类型"""
        if pd.isna(值) or 值 is None:
            return None
        elif isinstance(值, (np.integer, np.int64, np.int32, np.int16, np.int8)):
            return int(值)
        elif isinstance(值, (np.floating, np.float64, np.float32, np.float16)):
            if np.isnan(值):
                return None
            return float(值)
        elif isinstance(值, (np.bool_, bool)):
            return bool(值)
        elif isinstance(值, (np.str_, str)):
            return str(值)
        elif isinstance(值, bytes):
            return 值.decode('utf-8', errors='ignore')
        else:
            try:
                # 尝试转换为基本类型
                if hasattr(值, 'item'):  # numpy标量
                    return 值.item()
                else:
                    return str(值)
            except:
                return str(值) if 值 is not None else None

    def __init__(self, 配置文件路径="配置文件.json"):
        """初始化数据接口"""
        # 加载配置
        self.配置 = self._加载配置(配置文件路径)
        
        # 初始化数据库
        数据库文件 = self.配置["数据库配置"]["数据库文件"]
        self.db = 数据库管理器(数据库文件)
        
        # 创建Flask应用
        self.app = Flask(__name__)
        CORS(self.app)  # 允许跨域请求
        
        # 设置日志
        self.logger = logging.getLogger(__name__)
        
        # 注册路由
        self._注册路由()
        
        self.logger.info("网页数据接口初始化完成")
    
    def _加载配置(self, 配置文件路径):
        """加载配置文件"""
        try:
            with open(配置文件路径, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            raise
    
    def _注册路由(self):
        """注册API路由"""
        
        @self.app.route('/')
        def 首页():
            """返回主页面"""
            return send_from_directory('网页可视化', 'index.html')
        
        @self.app.route('/<path:filename>')
        def 静态文件(filename):
            """提供静态文件服务"""
            return send_from_directory('网页可视化', filename)
        
        @self.app.route('/api/stocks')
        def 获取股票列表():
            """获取股票列表API"""
            try:
                sql = """
                    SELECT ts_code, name, industry, area, market, list_date
                    FROM 股票基础信息
                    ORDER BY ts_code
                """
                数据 = self.db.查询数据(sql)
                
                股票列表 = []
                for _, row in 数据.iterrows():
                    股票列表.append({
                        'code': self.转换为JSON兼容类型(row['ts_code']),
                        'name': self.转换为JSON兼容类型(row['name']),
                        'industry': self.转换为JSON兼容类型(row['industry']),
                        'area': self.转换为JSON兼容类型(row['area']),
                        'market': self.转换为JSON兼容类型(row['market']),
                        'listDate': self.转换为JSON兼容类型(row['list_date'])
                    })

                return jsonify({
                    'success': True,
                    'data': 股票列表,
                    'total': self.转换为JSON兼容类型(len(股票列表))
                })
                
            except Exception as e:
                self.logger.error(f"获取股票列表失败: {e}")
                return jsonify({'success': False, 'error': str(e)})
        
        @self.app.route('/api/stock/<string:code>/daily')
        def 获取股票日线数据(code):
            """获取股票日线数据API"""
            try:
                # 获取查询参数
                开始日期 = request.args.get('start_date', '20240101')
                结束日期 = request.args.get('end_date', datetime.now().strftime('%Y%m%d'))
                
                sql = """
                    SELECT trade_date, open, high, low, close, pre_close, 
                           change, pct_chg, vol, amount
                    FROM 日线行情
                    WHERE ts_code = ? AND trade_date >= ? AND trade_date <= ?
                    ORDER BY trade_date
                """
                
                数据 = self.db.查询数据(sql, (code.upper(), 开始日期, 结束日期))
                
                if 数据.empty:
                    return jsonify({
                        'success': False,
                        'error': f'股票 {code} 无数据'
                    })
                
                # 转换数据格式
                日线数据 = []
                for _, row in 数据.iterrows():
                    日线数据.append({
                        'date': self.转换为JSON兼容类型(row['trade_date']),
                        'open': self.转换为JSON兼容类型(row['open']) if row['open'] else 0,
                        'high': self.转换为JSON兼容类型(row['high']) if row['high'] else 0,
                        'low': self.转换为JSON兼容类型(row['low']) if row['low'] else 0,
                        'close': self.转换为JSON兼容类型(row['close']) if row['close'] else 0,
                        'volume': self.转换为JSON兼容类型(row['vol']) if row['vol'] else 0,
                        'amount': self.转换为JSON兼容类型(row['amount']) if row['amount'] else 0,
                        'change': self.转换为JSON兼容类型(row['change']) if row['change'] else 0,
                        'pctChange': self.转换为JSON兼容类型(row['pct_chg']) if row['pct_chg'] else 0
                    })

                return jsonify({
                    'success': True,
                    'data': 日线数据,
                    'total': self.转换为JSON兼容类型(len(日线数据))
                })
                
            except Exception as e:
                self.logger.error(f"获取股票 {股票代码} 日线数据失败: {e}")
                return jsonify({'success': False, 'error': str(e)})
        
        @self.app.route('/api/market/overview')
        def 获取市场概览():
            """获取市场概览数据API"""
            try:
                # 获取最新交易日
                最新日期_sql = "SELECT MAX(trade_date) as latest_date FROM 日线行情"
                最新日期_结果 = self.db.查询数据(最新日期_sql)
                最新日期 = 最新日期_结果.iloc[0]['latest_date']
                
                # 获取当日市场数据
                sql = """
                    SELECT h.ts_code, b.name, b.industry, h.close, h.pre_close, 
                           h.pct_chg, h.vol, h.amount, h.high, h.low
                    FROM 日线行情 h
                    JOIN 股票基础信息 b ON h.ts_code = b.ts_code
                    WHERE h.trade_date = ?
                    ORDER BY h.amount DESC
                """
                
                数据 = self.db.查询数据(sql, (最新日期,))
                
                if 数据.empty:
                    return jsonify({
                        'success': False,
                        'error': '无市场数据'
                    })
                
                # 计算市场统计
                总股票数 = self.转换为JSON兼容类型(len(数据))
                上涨股票数 = self.转换为JSON兼容类型(len(数据[数据['pct_chg'] > 0]))
                下跌股票数 = self.转换为JSON兼容类型(len(数据[数据['pct_chg'] < 0]))
                平盘股票数 = self.转换为JSON兼容类型(总股票数 - 上涨股票数 - 下跌股票数)

                平均涨跌幅 = self.转换为JSON兼容类型(数据['pct_chg'].mean())
                总成交额 = self.转换为JSON兼容类型(数据['amount'].sum())
                
                # 涨跌分布
                涨跌分布 = {
                    '涨超5%': self.转换为JSON兼容类型(len(数据[数据['pct_chg'] > 5])),
                    '涨2-5%': self.转换为JSON兼容类型(len(数据[(数据['pct_chg'] > 2) & (数据['pct_chg'] <= 5)])),
                    '涨0-2%': self.转换为JSON兼容类型(len(数据[(数据['pct_chg'] > 0) & (数据['pct_chg'] <= 2)])),
                    '平盘': 平盘股票数,
                    '跌0-2%': self.转换为JSON兼容类型(len(数据[(数据['pct_chg'] < 0) & (数据['pct_chg'] >= -2)])),
                    '跌2-5%': self.转换为JSON兼容类型(len(数据[(数据['pct_chg'] < -2) & (数据['pct_chg'] >= -5)])),
                    '跌超5%': self.转换为JSON兼容类型(len(数据[数据['pct_chg'] < -5]))
                }
                
                # 成交额前20
                成交额前20 = []
                for _, row in 数据.head(20).iterrows():
                    成交额前20.append({
                        'code': self.转换为JSON兼容类型(row['ts_code']),
                        'name': self.转换为JSON兼容类型(row['name']),
                        'amount': self.转换为JSON兼容类型(row['amount']),
                        'pctChange': self.转换为JSON兼容类型(row['pct_chg'])
                    })
                
                # 行业统计
                行业数据 = 数据.groupby('industry').agg({
                    'pct_chg': 'mean',
                    'amount': 'sum',
                    'ts_code': 'count'
                }).reset_index()
                
                行业统计 = []
                for _, row in 行业数据.iterrows():
                    股票数量 = self.转换为JSON兼容类型(row['ts_code'])
                    if 股票数量 >= 3:  # 至少3只股票的行业
                        行业统计.append({
                            'industry': self.转换为JSON兼容类型(row['industry']),
                            'avgChange': self.转换为JSON兼容类型(row['pct_chg']),
                            'totalAmount': self.转换为JSON兼容类型(row['amount']),
                            'stockCount': 股票数量
                        })
                
                行业统计.sort(key=lambda x: x['avgChange'], reverse=True)
                
                return jsonify({
                    'success': True,
                    'data': {
                        'date': self.转换为JSON兼容类型(最新日期),
                        'summary': {
                            'totalStocks': 总股票数,
                            'upStocks': 上涨股票数,
                            'downStocks': 下跌股票数,
                            'flatStocks': 平盘股票数,
                            'avgChange': 平均涨跌幅,
                            'totalAmount': 总成交额
                        },
                        'distribution': 涨跌分布,
                        'topAmount': 成交额前20,
                        'industries': 行业统计[:15]  # 前15个行业
                    }
                })
                
            except Exception as e:
                self.logger.error(f"获取市场概览失败: {e}")
                return jsonify({'success': False, 'error': str(e)})
        
        @self.app.route('/api/database/status')
        def 获取数据库状态():
            """获取数据库状态API"""
            try:
                表信息 = self.db.获取所有表信息()
                
                状态数据 = {}
                for 表名, 信息 in 表信息.items():
                    if 表名 != "下载进度记录":
                        状态数据[表名] = {
                            'recordCount': self.转换为JSON兼容类型(信息.get('记录数', 0)),
                            'lastUpdate': self.转换为JSON兼容类型(信息.get('最新更新时间', '无')),
                            'fieldCount': self.转换为JSON兼容类型(信息.get('字段数', 0))
                        }
                
                return jsonify({
                    'success': True,
                    'data': 状态数据
                })
                
            except Exception as e:
                self.logger.error(f"获取数据库状态失败: {e}")
                return jsonify({'success': False, 'error': str(e)})

        @self.app.route('/api/stock/<string:code>/detail/<string:date>')
        def 获取股票详细数据(code, date):
            """获取特定股票特定日期的详细数据API"""
            try:
                sql = """
                    SELECT *
                    FROM 日线行情
                    WHERE ts_code = ? AND trade_date = ?
                """

                数据 = self.db.查询数据(sql, (code.upper(), date))

                if 数据.empty:
                    return jsonify({
                        'success': False,
                        'error': f'股票 {code} 在 {date} 无数据'
                    })

                # 获取第一行数据（应该只有一行）
                row = 数据.iloc[0]

                # 转换所有字段为详细数据
                详细数据 = {}
                for 字段名 in 数据.columns:
                    值 = row[字段名]
                    详细数据[字段名] = self.转换为JSON兼容类型(值)

                return jsonify({
                    'success': True,
                    'data': 详细数据
                })

            except Exception as e:
                self.logger.error(f"获取股票 {code} 在 {date} 的详细数据失败: {e}")
                return jsonify({'success': False, 'error': str(e)})

        @self.app.route('/api/all-stocks-data')
        def 获取所有股票数据():
            """获取所有股票数据API - 支持日期选择"""
            try:
                # 获取查询参数
                交易日期 = request.args.get('trade_date', None)

                # 如果没有指定日期，获取最新交易日
                if not 交易日期:
                    最新日期_sql = "SELECT MAX(trade_date) as latest_date FROM 日线行情"
                    最新日期_结果 = self.db.查询数据(最新日期_sql)

                    if 最新日期_结果.empty:
                        return jsonify({'success': False, 'error': '无交易数据'})

                    交易日期 = 最新日期_结果.iloc[0]['latest_date']

                # 调试信息：检查查询日期
                print(f"🔍 调试信息 - 查询交易日: {交易日期}")

                # 检查该日期有多少条数据
                日期数据统计_sql = "SELECT COUNT(*) as count FROM 日线行情 WHERE trade_date = ?"
                日期数据统计 = self.db.查询数据(日期数据统计_sql, (交易日期,))
                日期数据数量 = self.转换为JSON兼容类型(日期数据统计.iloc[0]['count'])
                print(f"🔍 调试信息 - {交易日期} 的数据量: {日期数据数量}")

                # 获取所有股票的指定日期数据，包含完整字段和财务指标
                sql = """
                    SELECT
                        s.ts_code,
                        s.symbol,
                        s.name,
                        s.area,
                        s.industry,
                        s.cnspell,
                        s.market,
                        s.list_date,
                        s.act_name,
                        s.act_ent_type,
                        d.trade_date,
                        d.open,
                        d.high,
                        d.low,
                        d.close,
                        d.pre_close,
                        d.change,
                        d.pct_chg,
                        d.vol,
                        d.amount,
                        m.pe,
                        m.pe_ttm,
                        m.pb,
                        m.ps,
                        m.ps_ttm,
                        m.dv_ratio,
                        m.dv_ttm,
                        m.total_mv,
                        m.circ_mv,
                        m.turnover_rate,
                        m.volume_ratio
                    FROM 股票基础信息 s
                    LEFT JOIN 日线行情 d ON s.ts_code = d.ts_code AND d.trade_date = ?
                    LEFT JOIN 每日指标 m ON s.ts_code = m.ts_code AND m.trade_date = ?
                    ORDER BY s.ts_code
                """

                数据 = self.db.查询数据(sql, (交易日期, 交易日期))
                print(f"🔍 调试信息 - 查询结果总数: {len(数据)}")

                # 检查有多少条记录有交易数据
                有数据记录 = 数据[数据['trade_date'].notna()]
                print(f"🔍 调试信息 - 有交易数据的记录数: {len(有数据记录)}")

                股票数据列表 = []
                for _, row in 数据.iterrows():
                    股票数据 = {}
                    # 使用新的转换函数处理所有字段
                    for 列名, 值 in row.items():
                        股票数据[列名] = self.转换为JSON兼容类型(值)

                    # 对于空值显示为'-'的字段
                    for 字段 in ['symbol', 'area', 'industry', 'cnspell', 'market', 'list_date', 'act_name', 'act_ent_type', 'trade_date']:
                        if 股票数据[字段] is None:
                            股票数据[字段] = '-'

                    股票数据列表.append(股票数据)

                return jsonify({
                    'success': True,
                    'data': 股票数据列表,
                    'total': self.转换为JSON兼容类型(len(股票数据列表)),
                    'query_date': self.转换为JSON兼容类型(交易日期),
                    'data_count': self.转换为JSON兼容类型(日期数据数量)
                })

            except Exception as e:
                self.logger.error(f"获取所有股票数据失败: {e}")
                return jsonify({'success': False, 'error': str(e)})

        @self.app.route('/api/available-dates')
        def 获取可用交易日期():
            """获取有数据的交易日期API - 只返回有实际交易数据的日期"""
            try:
                # 修改SQL查询，只返回有实际交易数据的日期，并统计每个日期的数据量
                sql = """
                    SELECT trade_date, COUNT(*) as data_count
                    FROM 日线行情
                    WHERE trade_date IS NOT NULL
                    AND close IS NOT NULL
                    GROUP BY trade_date
                    HAVING COUNT(*) > 0
                    ORDER BY trade_date DESC
                    LIMIT 100
                """

                数据 = self.db.查询数据(sql)

                # 调试信息
                print(f"🔍 可用交易日期查询结果: {len(数据)} 个日期")
                if not 数据.empty:
                    print(f"🔍 最新交易日: {数据.iloc[0]['trade_date']}, 数据量: {数据.iloc[0]['data_count']}")
                    print(f"🔍 最早交易日: {数据.iloc[-1]['trade_date']}, 数据量: {数据.iloc[-1]['data_count']}")

                日期列表 = []
                for _, row in 数据.iterrows():
                    日期 = self.转换为JSON兼容类型(row['trade_date'])
                    数据量 = self.转换为JSON兼容类型(row['data_count'])
                    日期列表.append(日期)

                    # 调试：显示前几个日期的数据量
                    if len(日期列表) <= 5:
                        print(f"🔍 日期 {日期}: {数据量} 条数据")

                return jsonify({
                    'success': True,
                    'dates': 日期列表,
                    'total': len(日期列表)
                })

            except Exception as e:
                self.logger.error(f"获取可用交易日期失败: {e}")
                return jsonify({'success': False, 'error': str(e)})

        @self.app.route('/api/trading-calendar')
        def 获取交易日历():
            """获取交易日历API"""
            try:
                # 获取查询参数
                开始日期 = request.args.get('start_date', '20240101')
                结束日期 = request.args.get('end_date', datetime.now().strftime('%Y%m%d'))

                sql = """
                    SELECT cal_date, is_open
                    FROM 交易日历
                    WHERE cal_date >= ? AND cal_date <= ? AND is_open = 1
                    ORDER BY cal_date
                """

                数据 = self.db.查询数据(sql, (开始日期, 结束日期))

                交易日列表 = []
                for _, row in 数据.iterrows():
                    交易日列表.append(self.转换为JSON兼容类型(row['cal_date']))

                return jsonify({
                    'success': True,
                    'data': 交易日列表,
                    'total': self.转换为JSON兼容类型(len(交易日列表))
                })

            except Exception as e:
                self.logger.error(f"获取交易日历失败: {e}")
                return jsonify({'success': False, 'error': str(e)})

    def 启动服务器(self, host='127.0.0.1', port=5000, debug=False):
        """启动Web服务器"""
        print(f"🌐 启动Web服务器...")
        print(f"📍 访问地址: http://{host}:{port}")
        print(f"🔧 调试模式: {'开启' if debug else '关闭'}")
        
        try:
            self.app.run(host=host, port=port, debug=debug)
        except Exception as e:
            self.logger.error(f"启动Web服务器失败: {e}")
            raise

def main():
    """主函数"""
    # 创建网页数据接口
    接口 = 网页数据接口()
    
    # 启动服务器
    接口.启动服务器(debug=True)

if __name__ == "__main__":
    main()
