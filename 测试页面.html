<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>修复测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            color: #333;
            margin-top: 0;
        }
        .date-selector {
            margin: 10px 0;
        }
        .date-selector select {
            padding: 8px;
            font-size: 14px;
            border: 1px solid #ccc;
            border-radius: 4px;
            min-width: 200px;
        }
        .table-container {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-top: 10px;
        }
        .table-wrapper {
            overflow-x: auto;
            min-height: 300px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            min-width: 800px;
        }
        table th, table td {
            padding: 8px 12px;
            border: 1px solid #ddd;
            text-align: left;
            white-space: nowrap;
        }
        table th {
            background: #f8f9fa;
            font-weight: bold;
            position: sticky;
            top: 0;
            z-index: 10;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .scroll-hint {
            background: linear-gradient(135deg, #3b82f6, #9333ea);
            color: white;
            padding: 10px;
            border-radius: 4px;
            text-align: center;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Web界面修复测试页面</h1>
        
        <div class="test-section">
            <h3>📅 日期选择器测试</h3>
            <div class="date-selector">
                <label for="date-select">选择交易日期：</label>
                <select id="date-select" onchange="loadDataForDate()">
                    <option value="">加载中...</option>
                </select>
            </div>
            <div id="date-status" class="status info">正在加载可用日期...</div>
        </div>

        <div class="test-section">
            <h3>📊 表格滚动测试</h3>
            <div class="scroll-hint">
                💡 提示：表格可以左右滑动查看更多列，水平滚动条已优化
            </div>
            <div class="table-container">
                <div class="table-wrapper">
                    <table id="data-table">
                        <thead>
                            <tr>
                                <th>股票代码</th>
                                <th>股票名称</th>
                                <th>行业</th>
                                <th>地区</th>
                                <th>市场</th>
                                <th>交易日期</th>
                                <th>开盘价</th>
                                <th>最高价</th>
                                <th>最低价</th>
                                <th>收盘价</th>
                                <th>涨跌幅(%)</th>
                                <th>成交量</th>
                                <th>成交额</th>
                                <th>市盈率</th>
                                <th>市净率</th>
                            </tr>
                        </thead>
                        <tbody id="table-body">
                            <tr><td colspan="15">正在加载数据...</td></tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div id="table-status" class="status info">等待数据加载...</div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://127.0.0.1:5000/api';
        let availableDates = [];
        let currentData = [];

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadAvailableDates();
        });

        // 加载可用日期
        async function loadAvailableDates() {
            try {
                const response = await fetch(`${API_BASE}/available-dates`);
                const data = await response.json();
                
                if (data.success) {
                    availableDates = data.dates;
                    populateDateSelector();
                    updateStatus('date-status', `成功加载 ${data.dates.length} 个可用交易日期`, 'success');
                    
                    // 自动加载最新日期的数据
                    loadDataForDate();
                } else {
                    updateStatus('date-status', `加载日期失败: ${data.error}`, 'error');
                }
            } catch (error) {
                updateStatus('date-status', `网络错误: ${error.message}`, 'error');
            }
        }

        // 填充日期选择器
        function populateDateSelector() {
            const selector = document.getElementById('date-select');
            selector.innerHTML = '<option value="">最新交易日</option>';
            
            availableDates.forEach((date, index) => {
                const option = document.createElement('option');
                option.value = date;
                const formattedDate = formatDate(date);
                option.textContent = index === 0 ? `${formattedDate} (最新)` : formattedDate;
                selector.appendChild(option);
            });
        }

        // 格式化日期显示
        function formatDate(dateStr) {
            if (!dateStr || dateStr.length !== 8) return dateStr;
            return `${dateStr.substr(0, 4)}-${dateStr.substr(4, 2)}-${dateStr.substr(6, 2)}`;
        }

        // 加载指定日期的数据
        async function loadDataForDate() {
            const selectedDate = document.getElementById('date-select').value;
            updateStatus('table-status', '正在加载数据...', 'info');
            
            try {
                let url = `${API_BASE}/all-stocks-data`;
                if (selectedDate) {
                    url += `?trade_date=${selectedDate}`;
                }
                
                const response = await fetch(url);
                const data = await response.json();
                
                if (data.success) {
                    currentData = data.data;
                    renderTable();
                    
                    const dateInfo = selectedDate ? formatDate(selectedDate) : '最新交易日';
                    updateStatus('table-status', 
                        `成功加载 ${dateInfo} 的数据：总计 ${data.total} 只股票，其中 ${data.data_count} 只有交易数据`, 
                        'success');
                } else {
                    updateStatus('table-status', `加载数据失败: ${data.error}`, 'error');
                }
            } catch (error) {
                updateStatus('table-status', `网络错误: ${error.message}`, 'error');
            }
        }

        // 渲染表格
        function renderTable() {
            const tbody = document.getElementById('table-body');
            tbody.innerHTML = '';
            
            // 只显示前50条数据以提高性能
            const displayData = currentData.slice(0, 50);
            
            displayData.forEach(stock => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${stock.ts_code || '-'}</td>
                    <td>${stock.name || '-'}</td>
                    <td>${stock.industry || '-'}</td>
                    <td>${stock.area || '-'}</td>
                    <td>${stock.market || '-'}</td>
                    <td>${stock.trade_date ? formatDate(stock.trade_date) : '-'}</td>
                    <td>${stock.open || '-'}</td>
                    <td>${stock.high || '-'}</td>
                    <td>${stock.low || '-'}</td>
                    <td>${stock.close || '-'}</td>
                    <td>${stock.pct_chg || '-'}</td>
                    <td>${stock.vol || '-'}</td>
                    <td>${stock.amount || '-'}</td>
                    <td>${stock.pe || '-'}</td>
                    <td>${stock.pb || '-'}</td>
                `;
                tbody.appendChild(row);
            });
            
            if (displayData.length === 0) {
                tbody.innerHTML = '<tr><td colspan="15">暂无数据</td></tr>';
            }
        }

        // 更新状态显示
        function updateStatus(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `status ${type}`;
        }
    </script>
</body>
</html>
