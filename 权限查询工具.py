#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Tushare权限查询工具
用于查看用户的API权限和可访问的数据接口
"""

import tushare as ts
import json
import pandas as pd
from datetime import datetime
import logging

class Tushare权限查询器:
    """Tushare权限查询器类"""
    
    def __init__(self, 配置文件路径="配置文件.json"):
        """
        初始化权限查询器
        
        参数:
            配置文件路径 (str): 配置文件路径
        """
        self.配置 = self._加载配置(配置文件路径)
        self.token = self.配置["tushare配置"]["api_token"]
        
        # 设置tushare token
        ts.set_token(self.token)
        self.pro = ts.pro_api()
        
        # 设置日志
        self._设置日志()
        
        # 定义所有可能的接口
        self.所有接口列表 = {
            "基础数据": [
                "stock_basic",      # 股票列表
                "trade_cal",        # 交易日历
                "namechange",       # 股票曾用名
                "hs_const",         # 沪深股通成分股
                "stk_limit",        # 涨跌停价格
                "stock_company",    # 上市公司基本信息
                "stk_managers",     # 上市公司管理层
                "stk_rewards",      # 管理层薪酬和持股
                "new_share",        # IPO新股列表
            ],
            "行情数据": [
                "daily",            # 日线行情
                "weekly",           # 周线行情
                "monthly",          # 月线行情
                "adj_factor",       # 复权因子
                "suspend_d",        # 每日停复牌信息
                "daily_basic",      # 每日指标
                "moneyflow",        # 个股资金流向
                "stk_limit",        # 每日涨跌停价格
                "limit_list_d",     # 每日涨跌停统计
            ],
            "财务数据": [
                "income",           # 利润表
                "balancesheet",     # 资产负债表
                "cashflow",         # 现金流量表
                "forecast",         # 业绩预告
                "express",          # 业绩快报
                "dividend",         # 分红送股
                "fina_indicator",   # 财务指标
                "fina_audit",       # 财务审计意见
                "fina_mainbz",      # 主营业务构成
            ],
            "市场参考数据": [
                "moneyflow_hsgt",   # 沪深港通资金流向
                "hsgt_top10",       # 沪深港通十大成交股
                "ggt_top10",        # 港股通十大成交股
                "margin",           # 融资融券交易汇总
                "margin_detail",    # 融资融券交易明细
                "top10_holders",    # 前十大股东
                "top10_floatholders", # 前十大流通股东
                "top_list",         # 龙虎榜每日明细
                "top_inst",         # 龙虎榜机构明细
            ],
            "指数数据": [
                "index_basic",      # 指数基本信息
                "index_daily",      # 指数日线行情
                "index_weekly",     # 指数周线行情
                "index_monthly",    # 指数月线行情
                "index_weight",     # 指数成分和权重
                "index_dailybasic", # 指数每日指标
                "index_classify",   # 申万行业分类
                "index_member",     # 申万行业成分
            ]
        }
    
    def _加载配置(self, 配置文件路径):
        """加载配置文件"""
        try:
            with open(配置文件路径, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            return {}
    
    def _设置日志(self):
        """设置日志配置"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('权限查询日志.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def 检查接口权限(self, 接口名称):
        """
        检查单个接口的访问权限
        
        参数:
            接口名称 (str): 要检查的接口名称
            
        返回:
            dict: 包含权限信息的字典
        """
        try:
            # 尝试调用接口获取少量数据
            if 接口名称 == "stock_basic":
                result = self.pro.stock_basic(list_status='L', limit=1)
            elif 接口名称 == "daily":
                result = self.pro.daily(trade_date='20240101', limit=1)
            elif 接口名称 == "trade_cal":
                result = self.pro.trade_cal(start_date='20240101', end_date='20240102')
            else:
                # 通用调用方式
                func = getattr(self.pro, 接口名称, None)
                if func:
                    result = func(limit=1)
                else:
                    return {"可访问": False, "错误": "接口不存在"}
            
            if result is not None and len(result) >= 0:
                return {
                    "可访问": True, 
                    "数据条数": len(result),
                    "字段列表": list(result.columns) if hasattr(result, 'columns') else []
                }
            else:
                return {"可访问": False, "错误": "返回数据为空"}
                
        except Exception as e:
            错误信息 = str(e)
            if "权限" in 错误信息 or "permission" in 错误信息.lower():
                return {"可访问": False, "错误": "权限不足"}
            elif "积分" in 错误信息 or "point" in 错误信息.lower():
                return {"可访问": False, "错误": "积分不足"}
            else:
                return {"可访问": False, "错误": 错误信息}
    
    def 查询所有权限(self):
        """
        查询用户的所有接口权限
        
        返回:
            dict: 包含所有权限信息的字典
        """
        self.logger.info("开始查询用户权限...")
        权限结果 = {}
        
        for 分类, 接口列表 in self.所有接口列表.items():
            self.logger.info(f"正在检查 {分类} 类别的接口...")
            权限结果[分类] = {}
            
            for 接口名称 in 接口列表:
                self.logger.info(f"检查接口: {接口名称}")
                权限信息 = self.检查接口权限(接口名称)
                权限结果[分类][接口名称] = 权限信息
                
                # 控制调用频率
                import time
                time.sleep(0.1)  # 避免调用过快
        
        return 权限结果
    
    def 生成权限报告(self, 权限结果, 保存文件="用户权限报告.txt"):
        """
        生成权限报告
        
        参数:
            权限结果 (dict): 权限查询结果
            保存文件 (str): 报告保存文件名
        """
        报告内容 = []
        报告内容.append("=" * 60)
        报告内容.append("Tushare用户权限报告")
        报告内容.append(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        报告内容.append("=" * 60)
        
        可访问接口总数 = 0
        不可访问接口总数 = 0
        
        for 分类, 接口字典 in 权限结果.items():
            报告内容.append(f"\n【{分类}】")
            报告内容.append("-" * 40)
            
            for 接口名称, 权限信息 in 接口字典.items():
                if 权限信息["可访问"]:
                    状态 = "✅ 可访问"
                    可访问接口总数 += 1
                    if "字段列表" in 权限信息:
                        字段信息 = f" (字段数: {len(权限信息['字段列表'])})"
                    else:
                        字段信息 = ""
                else:
                    状态 = f"❌ 不可访问 - {权限信息.get('错误', '未知错误')}"
                    不可访问接口总数 += 1
                    字段信息 = ""
                
                报告内容.append(f"  {接口名称:<20} {状态}{字段信息}")
        
        报告内容.append("\n" + "=" * 60)
        报告内容.append("权限统计:")
        报告内容.append(f"可访问接口数量: {可访问接口总数}")
        报告内容.append(f"不可访问接口数量: {不可访问接口总数}")
        报告内容.append(f"总接口数量: {可访问接口总数 + 不可访问接口总数}")
        报告内容.append("=" * 60)
        
        # 保存报告
        with open(保存文件, 'w', encoding='utf-8') as f:
            f.write('\n'.join(报告内容))
        
        # 打印到控制台
        print('\n'.join(报告内容))
        
        self.logger.info(f"权限报告已保存到: {保存文件}")
        return 可访问接口总数, 不可访问接口总数

def main():
    """主函数"""
    print("正在查询Tushare权限...")
    
    # 创建权限查询器
    查询器 = Tushare权限查询器()
    
    # 查询所有权限
    权限结果 = 查询器.查询所有权限()
    
    # 生成报告
    可访问数, 不可访问数 = 查询器.生成权限报告(权限结果)
    
    # 保存详细权限信息到JSON文件
    with open("详细权限信息.json", 'w', encoding='utf-8') as f:
        json.dump(权限结果, f, ensure_ascii=False, indent=2)
    
    print(f"\n权限查询完成！")
    print(f"可访问接口: {可访问数} 个")
    print(f"不可访问接口: {不可访问数} 个")
    print(f"详细信息已保存到: 用户权限报告.txt 和 详细权限信息.json")

if __name__ == "__main__":
    main()
