#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
启动Web可视化服务
稳定的Web服务器启动脚本
"""

import os
import sys
import time
import webbrowser
from threading import Timer

def 延迟打开浏览器():
    """延迟3秒后打开浏览器"""
    time.sleep(3)
    try:
        webbrowser.open("http://localhost:5000")
        print("🌐 浏览器已自动打开")
    except:
        print("⚠️  请手动打开浏览器访问: http://localhost:5000")

def main():
    """主函数"""
    print("🚀 启动Tushare股票数据Web可视化服务")
    print("=" * 50)
    print("📊 包含完整的PE、PB、股息率等财务指标数据")
    print("🌐 服务地址: http://localhost:5000")
    print("💡 按 Ctrl+C 可停止服务")
    print("=" * 50)
    
    # 在后台线程中延迟打开浏览器
    Timer(3.0, 延迟打开浏览器).start()
    
    try:
        # 启动Web服务器
        from 网页数据接口 import app
        app.run(host='0.0.0.0', port=5000, debug=False)
    except KeyboardInterrupt:
        print("\n👋 服务已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        print("💡 请确保没有其他程序占用5000端口")

if __name__ == "__main__":
    main()
